kind: ConfigMap
apiVersion: v1
metadata:
  name: ci_project_name
  namespace: ci_namespace
data:
  FIREBASE_SERVICE_ACCOUNT: <EMAIL>
  AUDIENCE: https://oauth2.googleapis.com/token
  SCOPE: https://www.googleapis.com/auth/firebase.remoteconfig
  FCM_SCOPE: https://www.googleapis.com/auth/firebase.messaging
  SEND_MESSAGE: https://fcm.googleapis.com/v1/projects/driven-mobile-qa/messages:send
  PRIVATE_KEY_ID: 2c8d5327482b6540141d28716682b16408c3bb08
  PROJECT_ID: driven-mobile-qa
  PUSH_OKTA_CLIENT_ID: 0oa9qstca02KIMLAB0x7
  PUSH_OKTA_CLIENT_SECRET: Cx7z5S6zh3jFYg7_TdhXvgIOh8tjNobLl9WAMBKpFA5MdbJamBwsJdL799TpzJ0i
  OKTA_INSTANCE: https://fleetcor-icd.oktapreview.com/oauth2/aus2l0d91irbotby80x7
