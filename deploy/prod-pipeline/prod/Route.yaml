---
kind: Route
apiVersion: route.openshift.io/v1
metadata:
  name: na-fleet-driven-mobile-push-notification-api
  namespace: na-fleet-driven-mobile-prod
  annotations:
    haproxy.router.openshift.io/rewrite-target: /  
    cert-utils-operator.redhat-cop.io/certs-from-secret: wildcard-fleetcor-com-internal     
spec:
  host: driven-mobile-mw.corpay.com
  path: /push
  to:
    kind: Service
    name: na-fleet-driven-mobile-push-notification-api
    weight: 100
  port:
    targetPort: 8080
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
  wildcardPolicy: None

---
kind: Route
apiVersion: route.openshift.io/v1
metadata:
  name: na-fleet-driven-mobile-push-notification-api
  namespace: na-fleet-driven-mobile-prod
spec:
  host: na-fleet-driven-push-api.apps.000-p-ea1-us.ocp.fleetcor.com
  path: /
  to:
    kind: Service
    name: na-fleet-driven-mobile-push-notification-api
    weight: 100
  port:
    targetPort: 8080
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
  wildcardPolicy: None