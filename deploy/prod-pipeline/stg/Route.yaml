---
kind: Route
apiVersion: route.openshift.io/v1
metadata:
  name: na-fleet-driven-mobile-push-notification-api
  namespace: na-fleet-driven-mobile-stg
  annotations:
    haproxy.router.openshift.io/rewrite-target: /
    cert-utils-operator.redhat-cop.io/certs-from-secret: wildcard-fleetcor-com-internal       
spec:
  host: driven-mobile-mw-stg.corpay.com
  path: /push
  to:
    kind: Service
    name: na-fleet-driven-mobile-push-notification-api
    weight: 100
  port:
    targetPort: 8080
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
  wildcardPolicy: None

---
kind: Route
apiVersion: route.openshift.io/v1
metadata:
  name: na-fleet-driven-mobile-push-notification-api
  namespace: na-fleet-driven-mobile-stg
spec:
  host: na-fleet-driven-push-api-stg.apps.000-p-ea1-us.ocp.fleetcor.com
  path: /
  to:
    kind: Service
    name: na-fleet-driven-mobile-push-notification-api
    weight: 100
  port:
    targetPort: 8080
  tls:
    termination: edge
    insecureEdgeTerminationPolicy: Redirect
  wildcardPolicy: None