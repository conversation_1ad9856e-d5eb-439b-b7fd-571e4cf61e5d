kind: Deployment
apiVersion: apps/v1
metadata:
  name: na-fleet-driven-mobile-push-notification-api
  namespace: na-fleet-driven-mobile-stg
spec:
  replicas: 1
  selector:
    matchLabels:
      app: na-fleet-driven-mobile-push-notification-api
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: na-fleet-driven-mobile-push-notification-api
    spec:
      containers:
        - name: na-fleet-driven-mobile-push-notification-api
          image: build_image
          ports:
            - containerPort: 8080
              protocol: TCP
          envFrom:
            - configMapRef:
                name: na-fleet-driven-mobile-push-notification-api
            - secretRef:
                name: na-fleet-driven-mobile-push-notification-api         
          resources: 
            limits: 
              cpu: 250m 
              memory: 225Mi 
            requests:
              cpu: 250m 
              memory: 225Mi
          readinessProbe:
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 2
          livenessProbe:
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 65
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 2
          imagePullPolicy: IfNotPresent
      restartPolicy: Always