## Builds: Python
## Deployments: OpenShift Pipeline
## 

## Support Documentation

- [ ] [Repository Design](https://confluence.fleetcor.com/display/CTO/GCTO+DevOps+Pipelines+-+Repository+Requirements)
- [ ] [GCTO DevSecOps Support Page](https://confluence.fleetcor.com/pages/viewpage.action?pageId=69641600)

## Setting up your repository

```
cd existing_repo
git remote add origin https://git.fleetcor.com/path/to/repository.git
git branch -M main
git push -uf origin main
```

### Variables to  define 
- $RUNNER_TAG 
- $DEV_OCP_API - URL to the OCP API
- $DEV_OCP_TOKEN - Login token for OCP
- $DEV_NAMESPACE - The namespace
- $QA_OCP_API - URL to the OCP API
- $QA_OCP_TOKEN - Login token for OCP
- $QA_NAMESPACE - The namespace (parent application group)

## GCTO Developer Pipelines

- [ ] [GCTO Pipeline Templates](https://git.fleetcor.com/gcto-pipelines/pipeline-templates/templates)
