const { IgnorePlugin } = require('webpack');
const path = require('path');
const nodeExternals = require('webpack-node-externals');
const { RunScriptWebpackPlugin } = require('run-script-webpack-plugin');

module.exports = {
  entry: ['./src/main.ts'],
  target: 'node',
  externals: [
    // nodeExternals({
    //   allowlist: ['webpack/hot/poll?100'],
    // }),
  ],
  module: {
    rules: [
      {
        test: /.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  mode: 'development',
  resolve: {
    extensions: ['.*', '.tsx', '.ts', '.js'],
  },
  plugins: [
    // new webpack.HotModuleReplacementPlugin(),
    // new RunScriptWebpackPlugin({ name: 'main.js', autoRestart: false }),
    // new webpack.DefinePlugin({
    //     'process.env.BUILD_DATE': JSON.stringify(date)
    //   }),
    new IgnorePlugin({
        checkResource(resource) {
          const lazyImports = [
            '@nestjs/microservices',
            '@nestjs/microservices/microservices-module',
            '@nestjs/platform-express',
            '@nestjs/websockets/socket-module',
            'amqp-connection-manager',
            'amqplib',
            'cache-manager',
            'cache-manager/package.json',
            'class-transformer/storage',
            'hbs',
            'ioredis',
            'kafkajs',
            'mqtt',
            'nats',
            'utf-8-validate',
            'bufferutil'
          ];
          if (!lazyImports.includes(resource)) {
            return false;
          }
          try {
            require.resolve(resource, { paths: [process.cwd()] });
          } catch (err) {
            return true;
          }
          return false;
        },
      }),
  ],
  output: {
    // path: path.join(__dirname, 'build'),
    path: path.resolve('build'),
    filename: 'main.js',
  },
};