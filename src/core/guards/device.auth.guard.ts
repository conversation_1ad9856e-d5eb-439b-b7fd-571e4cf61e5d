import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, InternalServerErrorException } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AppLogger } from '../logger/logger.service';

@Injectable()
export class DeviceAuthGuard implements CanActivate {

    constructor(private readonly appLogger: AppLogger,) { }

    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers.authorization;
        const bearerToken = authHeader && authHeader.split(' ')[1];
        const deviceId = request.headers.deviceId;
        if (!bearerToken || !deviceId) {
            throw new UnauthorizedException();
        }
        return true;
    }
}