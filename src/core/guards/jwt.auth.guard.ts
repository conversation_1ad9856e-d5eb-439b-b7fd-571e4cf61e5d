import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, InternalServerErrorException } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AppLogger } from '../logger/logger.service';
import { AuthService } from '../../modules/auth/auth.service';

@Injectable()
export class JWTAuthGuard implements CanActivate {

    constructor(private readonly appLogger: AppLogger, private readonly authService: AuthService) { }

    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers.authorization;
        const bearerToken = authHeader && authHeader.split(' ')[1];
        if (!bearerToken) {
            throw new UnauthorizedException();
        }
        return this.verifyToken(bearerToken);
    }

    async verifyToken(bearerToken: string) {
        try {
            const decodedToken = await this.authService.validateToken(bearerToken);
            this.appLogger.log("Decoded Token:", JSON.stringify(decodedToken));
            if (decodedToken.isExpired() || decodedToken.claims.sub != process.env.PUSH_OKTA_CLIENT_ID) {
                throw new UnauthorizedException();
            }

            return true;
        } catch (error) {
            this.appLogger.log("AuthGuard", JSON.stringify(error));
            throw new UnauthorizedException();
        }
    }
}