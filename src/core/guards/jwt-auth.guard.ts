import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    // if (!token) {
    //   throw new UnauthorizedException('No token provided');
    // }
    return true;
    // try {
    //   const payload = await this.jwtService.verifyAsync(token, {
    //     secret: this.configService.get<string>('JWT_SECRET'),
    //   });

    //   // Attach user to request
    //   request.user = payload;
    //   return true;
    // } catch (error) {
    //   throw new UnauthorizedException('Invalid token');
    // }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
