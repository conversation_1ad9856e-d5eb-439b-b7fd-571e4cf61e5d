import { Injectable } from '@nestjs/common';
import axios, { AxiosRequestConfig, InternalAxiosRequestConfig } from 'axios';
import { IHttpClient } from './interface/http.interface';
import { AppLogger } from '../logger/logger.service';
import { DrivenError } from '../exceptions/custom-error';
import { AxiosResponse } from 'axios';

const requestInterceptor = (config: InternalAxiosRequestConfig) => {
  // const requestId = `${generateRandomChars(8)}-BFF-${generateRandomChars(4)}-${generateRandomChars(8)}-${generateRandomChars(12)}`;
  config.headers['request-startTime'] = Date.now();
  // console.log(`Modern Request: ${config.url} : X-Request-ID: ${requestId}`);
  // config.headers['x-request-id'] = requestId;
  return config;
};

const responseInterceptor = (response: AxiosResponse) => {
  const startTime = response.config.headers['request-startTime'];
  const duration = Date.now() - startTime;
  console.log(`Modern Response:${response.config.url} ::  (duration: ${duration}ms)`);
  console.log(JSON.stringify(response.data));
  return response;
};

export const axiosInstance = axios.create({
  baseURL: process.env.MODERN_API_ENDPOINT, // Optional base URL
  timeout: 60 * 1000, // Optional timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

axiosInstance.interceptors.request.use(requestInterceptor);
axiosInstance.interceptors.response.use(responseInterceptor)

@Injectable()
export class MWHttpService implements IHttpClient {

  constructor(private readonly appLogger: AppLogger) { }

  get<T>(url: string): Promise<T> {
    return axiosInstance.get(url);
  }

  post<T, D>(url: string, data: D): Promise<T> {
    return axiosInstance.post(url, data);
  }

  put<T, D>(url: string, data: D): Promise<T> {
    return axiosInstance.put(url, data);
  }

  async makeRequest<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await axiosInstance.request(config);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        this.appLogger.error(`Modern API Error: ${JSON.stringify(error.response.data)}`);
        throw new DrivenError(error.response.statusText, error.response.status);
      }
      throw new error;
    }
  }

  createRequestConfig(
    method: string,//'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    body?: any,
    params?: Record<string, any>,
    headers?: Record<string, string>
  ): AxiosRequestConfig {
    const config: AxiosRequestConfig = {
      method: method,
      url: url,
      params: params || {},  // Query parameters
      data: body || {},      // Body payload for POST/PUT requests
      headers: headers || {} // Optional headers
    };
    return config;
  }
}