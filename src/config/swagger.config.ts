import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

export function setupSwagger(app: INestApplication): void {
  const config = new DocumentBuilder()
    .setTitle('AI Backend API')
    .setDescription(`
# AI-Powered Backend API

A comprehensive AI-powered backend service built with NestJS, providing intelligent chat capabilities, knowledge base management, and API integration features.

## Features

### 🤖 AI Chat & Conversation
- **Multi-Model Support**: GPT-4, GPT-3.5 Turbo, Claude 3 Sonnet
- **Streaming Responses**: Real-time chat with Server-Sent Events
- **Conversation Memory**: Persistent conversation context
- **Function Calling**: AI can execute predefined functions
- **Usage Analytics**: Track token consumption and costs

### 📚 Knowledge Base
- **Document Upload**: Support for various file formats
- **Vector Search**: Semantic search using embeddings
- **RAG Integration**: Retrieval-Augmented Generation
- **Document Management**: CRUD operations for knowledge base

### 🔗 API Integration
- **Dynamic API Calls**: AI can call external APIs
- **Schema Parsing**: Automatic API schema detection
- **Endpoint Management**: CRUD operations for API endpoints
- **Response Caching**: Intelligent caching for API responses

## Authentication

All endpoints require Bearer token authentication:

\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## Rate Limiting

API requests are rate-limited to ensure fair usage:
- **Chat endpoints**: 100 requests per minute
- **Other endpoints**: 1000 requests per minute

## Error Handling

The API uses standard HTTP status codes and returns detailed error messages:

\`\`\`json
{
  "statusCode": 400,
  "message": ["Validation error details"],
  "error": "Bad Request"
}
\`\`\`

## Streaming Responses

Chat endpoints support streaming responses using Server-Sent Events (SSE):

### POST /api/v1/ai/stream-chat
Returns streaming response with \`text/event-stream\` content type.

### GET /api/v1/ai/stream-chat-sse
Alternative streaming endpoint using GET method with query parameters.

## Getting Started

### Start the Application
\`\`\`bash
npm run start:ai:dev
\`\`\`

### Access Documentation
- **API Documentation**: http://localhost:3000/api/docs
- **Health Check**: http://localhost:3000/health

## Usage Examples

### Basic Chat
\`\`\`bash
curl -X POST "http://localhost:3000/api/v1/ai/chat" \\
  -H "Authorization: Bearer <token>" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": "Hello, how can you help me?",
    "model": "gpt-4",
    "temperature": 0.7
  }'
\`\`\`

### Streaming Chat
\`\`\`bash
curl -X POST "http://localhost:3000/api/v1/ai/stream-chat" \\
  -H "Authorization: Bearer <token>" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": "Tell me a story",
    "model": "gpt-4",
    "stream": true
  }'
\`\`\`

### Knowledge Base Search
\`\`\`bash
curl -X POST "http://localhost:3000/api/v1/knowledge-base/search" \\
  -H "Authorization: Bearer <token>" \\
  -H "Content-Type: application/json" \\
  -d '{
    "query": "machine learning",
    "limit": 5
  }'
\`\`\`
    `)
    .setVersion('1.0.0')
    .setContact(
      'AI Backend Team',
      'https://github.com/your-org/ai-backend',
      '<EMAIL>'
    )
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth'
    )
    .addTag('AI', 'AI chat and conversation endpoints')
    .addTag('Knowledge Base', 'Knowledge base and document management')
    .addTag('API Integration', 'External API integration and management')
    .addTag('Health', 'Health check and system status')
    .addServer('http://localhost:3000', 'Development server')
    .addServer('https://api.yourcompany.com', 'Production server')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
    deepScanRoutes: true,
  });

  // Customize the document
  document.info.termsOfService = 'https://yourcompany.com/terms';
  
  // Add global examples and schemas
  document.components = {
    ...document.components,
    examples: {
      ChatRequest: {
        summary: 'Basic chat request',
        value: {
          message: 'Hello, how can you help me today?',
          model: 'gpt-4',
          temperature: 0.7,
          maxTokens: 1000
        }
      },
      StreamChatRequest: {
        summary: 'Streaming chat request',
        value: {
          message: 'Tell me a story about AI',
          model: 'gpt-4',
          stream: true,
          temperature: 0.8
        }
      },
      ChatResponse: {
        summary: 'Successful chat response',
        value: {
          message: 'Hello! I\'m an AI assistant. I can help you with various tasks including answering questions, writing, analysis, and more. What would you like to know or discuss?',
          conversationId: 'conv_123456789',
          messageId: 'msg_987654321',
          model: 'gpt-4',
          usage: {
            promptTokens: 15,
            completionTokens: 35,
            totalTokens: 50
          },
          cost: 0.0015,
          responseTime: 1250
        }
      }
    }
  };

  SwaggerModule.setup('api/docs', app, document, {
    explorer: true,
    customSiteTitle: 'AI Backend API Documentation',
    customfavIcon: '/favicon.ico',
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info { margin: 50px 0 }
      .swagger-ui .info .title { color: #2c3e50 }
      .swagger-ui .scheme-container { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0 }
    `,
    customJs: '/api/docs/swagger-ui-init.js',
    swaggerOptions: {
      url: '/api/docs-json',
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
      filter: true,
      showRequestHeaders: true,
      tryItOutEnabled: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });
}
