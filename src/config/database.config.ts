import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { User } from '../entities/user.entity';
import { Conversation } from '../entities/conversation.entity';
import { Message } from '../entities/message.entity';
import { KnowledgeBase } from '../entities/knowledge-base.entity';
import { ApiEndpoint } from '../entities/api-endpoint.entity';

export const getDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => ({
  type: 'mysql',
  host: configService.get<string>('DB_HOST', 'localhost'),
  port: configService.get<number>('DB_PORT', 3306),
  username: configService.get<string>('DB_USERNAME', 'root'),
  password: configService.get<string>('DB_PASSWORD', ''),
  database: configService.get<string>('DB_DATABASE', 'ai_backend'),
  entities: [User, Conversation, Message, KnowledgeBase, ApiEndpoint],
  synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
  logging: configService.get<boolean>('DB_LOGGING', false),
  migrations: ['dist/migrations/*.js'],
  migrationsTableName: 'migrations',
  migrationsRun: configService.get<boolean>('DB_MIGRATIONS_RUN', false),
  ssl: configService.get<boolean>('DB_SSL', false) ? {
    rejectUnauthorized: false,
  } : false,
  extra: {
    connectionLimit: configService.get<number>('DB_CONNECTION_LIMIT', 10),
  },
});
