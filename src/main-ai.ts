import { NestFactory } from '@nestjs/core';
import { Val<PERSON>tionPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestExpressApplication } from '@nestjs/platform-express';
import helmet from 'helmet';
import * as compression from 'compression';

import { AIAppModule } from './ai-app.module';
import { setupSwagger } from './config/swagger.config';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  const app = await NestFactory.create<NestExpressApplication>(AIAppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  const configService = app.get(ConfigService);

  // Security with relaxed CSP for Swagger
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "http://localhost:3000", "https://localhost:3000"],
        fontSrc: ["'self'", "https:", "data:"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'self'"],
      },
    },
  }));

  // Compression
  app.use(compression());

  // CORS
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // API prefix
  app.setGlobalPrefix('api/v1');

  // Static files for Swagger UI - serve at /api/docs/ to match Swagger HTML paths
  app.useStaticAssets('node_modules/swagger-ui-dist', {
    prefix: '/api/docs/',
    setHeaders: (res, path) => {
      const normalizedPath = path.toLowerCase();
      if (normalizedPath.endsWith('.css')) {
        res.setHeader('Content-Type', 'text/css');
      } else if (normalizedPath.endsWith('.js')) {
        res.setHeader('Content-Type', 'application/javascript');
      } else if (normalizedPath.endsWith('.html')) {
        res.setHeader('Content-Type', 'text/html');
      } else if (normalizedPath.endsWith('.png')) {
        res.setHeader('Content-Type', 'image/png');
      } else if (normalizedPath.endsWith('.svg')) {
        res.setHeader('Content-Type', 'image/svg+xml');
      }
    },
  });

  // Custom swagger-ui-init.js endpoint
  app.getHttpAdapter().get('/api/docs/swagger-ui-init.js', (req: any, res: any) => {
    res.setHeader('Content-Type', 'application/javascript');
    res.send(`
window.onload = function() {
  // Wait for SwaggerUIBundle to be available
  if (typeof SwaggerUIBundle === 'undefined') {
    setTimeout(arguments.callee, 100);
    return;
  }

  const ui = SwaggerUIBundle({
    url: '/api/docs-json',
    dom_id: '#swagger-ui',
    deepLinking: true,
    presets: [
      SwaggerUIBundle.presets.apis,
      SwaggerUIStandalonePreset
    ],
    plugins: [
      SwaggerUIBundle.plugins.DownloadUrl
    ],
    layout: "StandaloneLayout",
    persistAuthorization: true,
    displayRequestDuration: true,
    docExpansion: 'none',
    filter: true,
    showRequestHeaders: true,
    tryItOutEnabled: true
  });

  window.ui = ui;
};
    `);
  });

  // Swagger documentation
  setupSwagger(app);

  // Health check endpoint
  app.getHttpAdapter().get('/health', (req: any, res: any) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: configService.get<string>('NODE_ENV', 'development'),
    });
  });

  const port = configService.get<number>('PORT', 3000);
  const host = configService.get<string>('HOST', '0.0.0.0');

  await app.listen(port, host);

  logger.log(`🚀 AI Backend API is running on: http://${host}:${port}`);
  logger.log(`📚 API Documentation: http://${host}:${port}/api/docs`);
  logger.log(`💚 Health Check: http://${host}:${port}/health`);
  logger.log(`🌍 Environment: ${configService.get<string>('NODE_ENV', 'development')}`);
}

bootstrap().catch((error) => {
  Logger.error('❌ Error starting server', error, 'Bootstrap');
  process.exit(1);
});
