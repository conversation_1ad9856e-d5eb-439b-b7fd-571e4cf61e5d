import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}

export enum EndpointStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPRECATED = 'deprecated',
  TESTING = 'testing',
}

@Entity('api_endpoints')
@Index(['method', 'path'])
@Index(['status'])
@Index(['createdAt'])
export class ApiEndpoint {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: HttpMethod,
  })
  method: HttpMethod;

  @Column()
  path: string;

  @Column()
  baseUrl: string;

  @Column({
    type: 'enum',
    enum: EndpointStatus,
    default: EndpointStatus.ACTIVE,
  })
  status: EndpointStatus;

  @Column({ type: 'json', nullable: true })
  requestSchema: {
    headers?: Record<string, any>;
    queryParams?: Record<string, any>;
    pathParams?: Record<string, any>;
    body?: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true })
  responseSchema: {
    success?: Record<string, any>;
    error?: Record<string, any>;
    examples?: Array<{
      status: number;
      description: string;
      body: any;
    }>;
  };

  @Column({ type: 'json', nullable: true })
  authentication: {
    type: 'bearer' | 'basic' | 'apikey' | 'oauth2' | 'none';
    required: boolean;
    description?: string;
  };

  @Column({ type: 'json', nullable: true })
  rateLimits: {
    requestsPerMinute?: number;
    requestsPerHour?: number;
    requestsPerDay?: number;
  };

  @Column({ type: 'json', nullable: true })
  metadata: {
    tags?: string[];
    category?: string;
    version?: string;
    deprecated?: boolean;
    deprecationDate?: string;
    replacementEndpoint?: string;
  };

  @Column({ type: 'json', nullable: true })
  usage: {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageResponseTime: number;
    lastUsed?: Date;
  };

  @Column({ type: 'int', default: 0 })
  callCount: number;

  @Column({ type: 'int', default: 0 })
  successCount: number;

  @Column({ type: 'int', default: 0 })
  errorCount: number;

  @Column({ type: 'float', nullable: true })
  averageResponseTime: number;

  @Column({ type: 'timestamp', nullable: true })
  lastUsedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Foreign Keys
  @Column()
  createdById: string;

  @Column({ nullable: true })
  updatedById: string;

  // Relationships
  @ManyToOne(() => User, (user) => user.createdApiEndpoints)
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updatedById' })
  updatedBy: User;
}
