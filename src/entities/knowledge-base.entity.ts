import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

export enum DocumentType {
  API_DOCUMENTATION = 'api_documentation',
  USER_MANUAL = 'user_manual',
  FAQ = 'faq',
  KNOWLEDGE_ARTICLE = 'knowledge_article',
  CODE_SNIPPET = 'code_snippet',
  TUTORIAL = 'tutorial',
}

export enum DocumentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  INDEXED = 'indexed',
  FAILED = 'failed',
  ARCHIVED = 'archived',
}

@Entity('knowledge_base')
@Index(['status'])
@Index(['type'])
@Index(['createdAt'])
export class KnowledgeBase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
  })
  type: DocumentType;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.PENDING,
  })
  status: DocumentStatus;

  @Column({ type: 'text', nullable: true })
  summary: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    source?: string;
    url?: string;
    version?: string;
    tags?: string[];
    category?: string;
    language?: string;
    fileType?: string;
    fileSize?: number;
  };

  @Column({ type: 'json', nullable: true })
  chunks: Array<{
    id: string;
    content: string;
    startIndex: number;
    endIndex: number;
    tokens: number;
  }>;

  @Column({ type: 'json', nullable: true })
  embeddings: {
    model: string;
    dimensions: number;
    vectors: number[][];
    chunkIds: string[];
  };

  @Column({ type: 'json', nullable: true })
  vectorIds: {
    pineconeIds?: string[];
    weaviateIds?: string[];
    localIds?: string[];
  };

  @Column({ type: 'int', nullable: true })
  tokenCount: number;

  @Column({ type: 'int', default: 0 })
  accessCount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastAccessedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  indexedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Foreign Keys
  @Column()
  createdById: string;

  @Column({ nullable: true })
  updatedById: string;

  // Relationships
  @ManyToOne(() => User, (user) => user.createdApiEndpoints)
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updatedById' })
  updatedBy: User;
}
