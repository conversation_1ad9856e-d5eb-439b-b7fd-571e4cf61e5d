import { Module } from '@nestjs/common';

import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ThrottlerModule } from '@nestjs/throttler';
import { CacheModule } from '@nestjs/cache-manager';
import { ScheduleModule } from '@nestjs/schedule';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { AIModule } from './modules/ai/ai.module';
import { KnowledgeBaseModule } from './modules/knowledge-base/knowledge-base.module';
import { ApiIntegrationModule } from './modules/api-integration/api-integration.module';



@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Event System
    EventEmitterModule.forRoot(),

    // Rate Limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => [{
        ttl: parseInt(configService.get<string>('THROTTLE_TTL', '60'), 10) * 1000,
        limit: parseInt(configService.get<string>('THROTTLE_LIMIT', '100'), 10),
      }],
    }),

    // Caching
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ttl: parseInt(configService.get<string>('CACHE_TTL', '300'), 10),
        max: parseInt(configService.get<string>('CACHE_MAX_ITEMS', '1000'), 10),
      }),
      isGlobal: true,
    }),

    // Task Scheduling
    ScheduleModule.forRoot(),

    // Authentication
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'your-secret-key'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
        },
      }),
      global: true,
    }),

    // Core Modules
    AIModule,
    KnowledgeBaseModule,
    ApiIntegrationModule,
  ],
  controllers: [],
  providers: [],
})
export class AIAppModule {}
