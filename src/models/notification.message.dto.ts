import { ApiProperty } from '@nestjs/swagger';

export class NotificationMessage {
  @ApiProperty()
  token: string;

  @ApiProperty()
  notification: {
    message: string;
    title: string;
  };

  @ApiProperty()
  data: {
    content: string;
  };

  @ApiProperty()
  android: {
    priority: string;
    ttl: string;
  };

  @ApiProperty()
  apns: {
    headers: {
      'apns-priority': string;
      'apns-expiration': string;
    };
  };
}