import { PickType } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty, ValidateNested } from "class-validator";
import { Type } from 'class-transformer';

export class NotificationDto {
  @IsNotEmpty()
  deviceToken: string;
  @IsNotEmpty()
  title: string;
  @IsNotEmpty()
  message: string;
  data: any;
}

export class MultipleDeviceNotificationDto extends PickType(NotificationDto, [
  "title",
  "message",
]) {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IdsDto)
  ids: IdsDto[];
  data: any;
}

export class TopicNotificationDto extends PickType(NotificationDto, [
  "title",
  "message",
]) {
  @IsNotEmpty()
  topic: string;
}

export class IdsDto {
  @IsNotEmpty()
  deviceToken: string;

  @IsNotEmpty()
  fuel_session_id: string;
}