import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>num, IsArray, <PERSON>N<PERSON>ber, IsBoolean, ValidateNested, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentType } from '../../../entities';

export class UploadDocumentDto {
  @ApiProperty({ description: 'Document title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Document content' })
  @IsString()
  content: string;

  @ApiProperty({ description: 'Document type', enum: DocumentType })
  @IsEnum(DocumentType)
  type: DocumentType;

  @ApiPropertyOptional({ description: 'Document summary' })
  @IsOptional()
  @IsString()
  summary?: string;

  @ApiPropertyOptional({ description: 'Document metadata' })
  @IsOptional()
  metadata?: {
    source?: string;
    url?: string;
    version?: string;
    tags?: string[];
    category?: string;
    language?: string;
  };
}

export class SearchKnowledgeBaseDto {
  @ApiProperty({ description: 'Search query' })
  @IsString()
  query: string;

  @ApiPropertyOptional({ description: 'Maximum number of results', minimum: 1, maximum: 50 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Minimum similarity threshold', minimum: 0, maximum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold?: number = 0.7;

  @ApiPropertyOptional({ description: 'Document types to search', enum: DocumentType, isArray: true })
  @IsOptional()
  @IsArray()
  @IsEnum(DocumentType, { each: true })
  types?: DocumentType[];

  @ApiPropertyOptional({ description: 'Tags to filter by' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Include document content in results' })
  @IsOptional()
  @IsBoolean()
  includeContent?: boolean = false;
}

export class KnowledgeBaseSearchResult {
  @ApiProperty({ description: 'Document ID' })
  id: string;

  @ApiProperty({ description: 'Document title' })
  title: string;

  @ApiProperty({ description: 'Document type' })
  type: DocumentType;

  @ApiProperty({ description: 'Similarity score' })
  score: number;

  @ApiProperty({ description: 'Relevant content chunks' })
  chunks: Array<{
    id: string;
    content: string;
    score: number;
  }>;

  @ApiPropertyOptional({ description: 'Full document content' })
  content?: string;

  @ApiProperty({ description: 'Document metadata' })
  metadata: any;

  @ApiProperty({ description: 'Document summary' })
  summary?: string;
}

export class KnowledgeBaseSearchResponse {
  @ApiProperty({ description: 'Search results', type: [KnowledgeBaseSearchResult] })
  results: KnowledgeBaseSearchResult[];

  @ApiProperty({ description: 'Total number of results' })
  total?: number;

  @ApiProperty({ description: 'Total results (alternative name for compatibility)' })
  totalResults?: number;

  @ApiProperty({ description: 'Search query' })
  query: string;

  @ApiPropertyOptional({ description: 'Search parameters used' })
  parameters?: {
    limit: number;
    threshold: number;
    types?: DocumentType[];
    tags?: string[];
  };

  @ApiProperty({ description: 'Search execution time in milliseconds' })
  executionTime?: number;

  @ApiProperty({ description: 'Search time (alternative name for compatibility)' })
  searchTime?: number;
}

export class DocumentChunkDto {
  @ApiProperty({ description: 'Chunk ID' })
  id: string;

  @ApiProperty({ description: 'Chunk content' })
  content: string;

  @ApiProperty({ description: 'Start index in original document' })
  startIndex: number;

  @ApiProperty({ description: 'End index in original document' })
  endIndex: number;

  @ApiProperty({ description: 'Number of tokens in chunk' })
  tokens: number;
}

export class ProcessDocumentDto {
  @ApiProperty({ description: 'Document ID to process' })
  @IsString()
  documentId: string;

  @ApiPropertyOptional({ description: 'Force reprocessing even if already processed' })
  @IsOptional()
  @IsBoolean()
  forceReprocess?: boolean = false;

  @ApiPropertyOptional({ description: 'Chunk size for document splitting' })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Max(2000)
  chunkSize?: number = 1000;

  @ApiPropertyOptional({ description: 'Chunk overlap size' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(500)
  chunkOverlap?: number = 200;
}
