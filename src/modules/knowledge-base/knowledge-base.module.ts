import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { KnowledgeBase } from '../../entities';
import { KnowledgeBaseService } from './services/knowledge-base.service';
import { EmbeddingService } from './services/embedding.service';
import { DocumentProcessorService } from './services/document-processor.service';
import { PineconeService } from './services/vector-store/pinecone.service';
import { KnowledgeBaseController } from './controllers/knowledge-base.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeBase]),
    ConfigModule,
    EventEmitterModule,
  ],
  controllers: [KnowledgeBaseController],
  providers: [
    KnowledgeBaseService,
    EmbeddingService,
    DocumentProcessorService,
    PineconeService,
  ],
  exports: [
    KnowledgeBaseService,
    EmbeddingService,
    DocumentProcessorService,
    PineconeService,
  ],
})
export class KnowledgeBaseModule {}
