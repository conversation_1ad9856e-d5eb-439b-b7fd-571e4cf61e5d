export interface VectorEmbedding {
  id: string;
  vector: number[];
  metadata: {
    documentId: string;
    chunkId: string;
    content: string;
    title: string;
    type: string;
    [key: string]: any;
  };
}

export interface VectorSearchResult {
  id: string;
  score: number;
  metadata: {
    documentId: string;
    chunkId: string;
    content: string;
    title: string;
    type: string;
    [key: string]: any;
  };
}

export interface VectorSearchOptions {
  topK: number;
  threshold?: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
  includeValues?: boolean;
}

export interface IVectorStore {
  // Initialize the vector store
  initialize(): Promise<void>;

  // Upsert vectors
  upsert(embeddings: VectorEmbedding[]): Promise<void>;

  // Search for similar vectors
  search(queryVector: number[], options: VectorSearchOptions): Promise<VectorSearchResult[]>;

  // Delete vectors
  delete(ids: string[]): Promise<void>;

  // Delete vectors by filter
  deleteByFilter(filter: Record<string, any>): Promise<void>;

  // Get vector store statistics
  getStats(): Promise<{
    totalVectors: number;
    dimensions: number;
    indexSize: number;
  }>;

  // Health check
  healthCheck(): Promise<boolean>;
}
