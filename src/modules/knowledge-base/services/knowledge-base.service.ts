import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';

import { KnowledgeBase, DocumentStatus, DocumentType } from '../../../entities';
import { EmbeddingService } from './embedding.service';
import { DocumentProcessorService } from './document-processor.service';
import { PineconeService } from './vector-store/pinecone.service';
import { 
  UploadDocumentDto, 
  SearchKnowledgeBaseDto, 
  KnowledgeBaseSearchResponse,
  ProcessDocumentDto 
} from '../dto/knowledge-base.dto';
import { VectorEmbedding } from '../interfaces/vector-store.interface';

@Injectable()
export class KnowledgeBaseService {
  private readonly logger = new Logger(KnowledgeBaseService.name);

  constructor(
    @InjectRepository(KnowledgeBase)
    private knowledgeBaseRepository: Repository<KnowledgeBase>,
    private embeddingService: EmbeddingService,
    private documentProcessor: DocumentProcessorService,
    private vectorStore: PineconeService,
    private eventEmitter: EventEmitter2,
  ) {}

  async uploadDocument(userId: string, uploadDto: UploadDocumentDto): Promise<KnowledgeBase> {
    try {
      // Extract metadata from content
      const contentMetadata = await this.documentProcessor.extractMetadata(uploadDto.content);

      // Create knowledge base entry
      const document = this.knowledgeBaseRepository.create({
        id: uuidv4(),
        title: uploadDto.title,
        content: uploadDto.content,
        type: uploadDto.type,
        summary: uploadDto.summary,
        status: DocumentStatus.PENDING,
        metadata: {
          ...uploadDto.metadata,
          ...contentMetadata,
        },
        createdById: userId,
        tokenCount: contentMetadata.estimatedTokens,
      });

      const savedDocument = await this.knowledgeBaseRepository.save(document);

      // Process document asynchronously
      this.processDocumentAsync(savedDocument.id);

      this.logger.log(`Document uploaded: ${savedDocument.id}`);
      return savedDocument;
    } catch (error) {
      this.logger.error('Error uploading document:', error);
      throw error;
    }
  }

  async processDocument(processDto: ProcessDocumentDto): Promise<void> {
    const document = await this.knowledgeBaseRepository.findOne({
      where: { id: processDto.documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    if (document.status === DocumentStatus.INDEXED && !processDto.forceReprocess) {
      this.logger.log(`Document already processed: ${document.id}`);
      return;
    }

    try {
      // Update status to processing
      await this.knowledgeBaseRepository.update(document.id, {
        status: DocumentStatus.PROCESSING,
      });

      // Process document into chunks
      const chunks = await this.documentProcessor.processDocument(document.content, {
        chunkSize: processDto.chunkSize,
        chunkOverlap: processDto.chunkOverlap,
      });

      // Generate embeddings for chunks
      const chunkTexts = chunks.map(chunk => chunk.content);
      const embeddings = await this.embeddingService.generateBatchEmbeddings(chunkTexts);

      // Prepare vector embeddings
      const vectorEmbeddings: VectorEmbedding[] = chunks.map((chunk, index) => ({
        id: `${document.id}_${chunk.id}`,
        vector: embeddings[index].embedding,
        metadata: {
          documentId: document.id,
          chunkId: chunk.id,
          content: chunk.content,
          title: document.title,
          type: document.type,
          startIndex: chunk.startIndex,
          endIndex: chunk.endIndex,
          tokens: chunk.tokens,
        },
      }));

      // Store in vector database
      await this.vectorStore.upsert(vectorEmbeddings);

      // Update document with chunks and embeddings info
      await this.knowledgeBaseRepository.update(document.id, {
        status: DocumentStatus.INDEXED,
        chunks,
        embeddings: {
          model: embeddings[0].model,
          dimensions: this.embeddingService.getEmbeddingDimensions(),
          vectors: embeddings.map(e => e.embedding),
          chunkIds: chunks.map(c => c.id),
        },
        vectorIds: {
          pineconeIds: vectorEmbeddings.map(v => v.id),
        },
        indexedAt: new Date(),
      });

      this.eventEmitter.emit('knowledge-base.document.indexed', {
        documentId: document.id,
        chunksCount: chunks.length,
        embeddingsCount: embeddings.length,
      });

      this.logger.log(`Document processed successfully: ${document.id}`);
    } catch (error) {
      this.logger.error(`Error processing document ${document.id}:`, error);
      
      await this.knowledgeBaseRepository.update(document.id, {
        status: DocumentStatus.FAILED,
      });
      
      throw error;
    }
  }

  async searchKnowledgeBase(searchDto: SearchKnowledgeBaseDto): Promise<KnowledgeBaseSearchResponse> {
    const startTime = Date.now();

    try {
      // Generate embedding for search query
      const queryEmbedding = await this.embeddingService.generateEmbedding(searchDto.query);

      // Prepare search filters
      const filter: Record<string, any> = {};
      if (searchDto.types && searchDto.types.length > 0) {
        filter.type = { $in: searchDto.types };
      }
      if (searchDto.tags && searchDto.tags.length > 0) {
        filter.tags = { $in: searchDto.tags };
      }

      // Search vector store
      const vectorResults = await this.vectorStore.search(queryEmbedding.embedding, {
        topK: searchDto.limit || 10,
        threshold: searchDto.threshold || 0.7,
        filter: Object.keys(filter).length > 0 ? filter : undefined,
        includeMetadata: true,
      });

      // Group results by document
      const documentGroups = new Map<string, any[]>();
      vectorResults.forEach(result => {
        const docId = result.metadata.documentId;
        if (!documentGroups.has(docId)) {
          documentGroups.set(docId, []);
        }
        documentGroups.get(docId)!.push(result);
      });

      // Get document details
      const documentIds = Array.from(documentGroups.keys());
      const documents = await this.knowledgeBaseRepository.find({
        where: { id: In(documentIds) },
      });

      // Build search results
      const results = documents.map(doc => {
        const chunks = documentGroups.get(doc.id) || [];
        const maxScore = Math.max(...chunks.map(c => c.score));

        return {
          id: doc.id,
          title: doc.title,
          type: doc.type,
          score: maxScore,
          chunks: chunks.map(chunk => ({
            id: chunk.metadata.chunkId,
            content: chunk.metadata.content,
            score: chunk.score,
          })),
          content: searchDto.includeContent ? doc.content : undefined,
          metadata: doc.metadata,
          summary: doc.summary,
        };
      });

      // Sort by score
      results.sort((a, b) => b.score - a.score);

      const response: KnowledgeBaseSearchResponse = {
        results,
        total: results.length,
        query: searchDto.query,
        parameters: {
          limit: searchDto.limit || 10,
          threshold: searchDto.threshold || 0.7,
          types: searchDto.types,
          tags: searchDto.tags,
        },
        executionTime: Date.now() - startTime,
      };

      this.logger.log(`Knowledge base search completed: ${results.length} results in ${response.executionTime}ms`);
      return response;
    } catch (error) {
      this.logger.error('Error searching knowledge base:', error);
      throw error;
    }
  }

  async getDocument(id: string): Promise<KnowledgeBase> {
    const document = await this.knowledgeBaseRepository.findOne({
      where: { id },
      relations: ['createdBy'],
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Update access count
    await this.knowledgeBaseRepository.update(id, {
      accessCount: document.accessCount + 1,
      lastAccessedAt: new Date(),
    });

    return document;
  }

  async deleteDocument(id: string): Promise<void> {
    const document = await this.knowledgeBaseRepository.findOne({
      where: { id },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    try {
      // Delete from vector store
      if (document.vectorIds?.pineconeIds) {
        await this.vectorStore.delete(document.vectorIds.pineconeIds);
      }

      // Delete from database
      await this.knowledgeBaseRepository.delete(id);

      this.eventEmitter.emit('knowledge-base.document.deleted', {
        documentId: id,
      });

      this.logger.log(`Document deleted: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting document ${id}:`, error);
      throw error;
    }
  }

  async getDocumentsByType(type: DocumentType): Promise<KnowledgeBase[]> {
    return await this.knowledgeBaseRepository.find({
      where: { type, status: DocumentStatus.INDEXED },
      order: { createdAt: 'DESC' },
    });
  }

  async getStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<DocumentType, number>;
    documentsByStatus: Record<DocumentStatus, number>;
    totalTokens: number;
    vectorStoreStats: any;
  }> {
    const [documents, vectorStoreStats] = await Promise.all([
      this.knowledgeBaseRepository.find(),
      this.vectorStore.getStats(),
    ]);

    const documentsByType = documents.reduce((acc, doc) => {
      acc[doc.type] = (acc[doc.type] || 0) + 1;
      return acc;
    }, {} as Record<DocumentType, number>);

    const documentsByStatus = documents.reduce((acc, doc) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1;
      return acc;
    }, {} as Record<DocumentStatus, number>);

    const totalTokens = documents.reduce((sum, doc) => sum + (doc.tokenCount || 0), 0);

    return {
      totalDocuments: documents.length,
      documentsByType,
      documentsByStatus,
      totalTokens,
      vectorStoreStats,
    };
  }

  private async processDocumentAsync(documentId: string): Promise<void> {
    // Process document in background
    setTimeout(async () => {
      try {
        await this.processDocument({ documentId });
      } catch (error) {
        this.logger.error(`Background processing failed for document ${documentId}:`, error);
      }
    }, 1000);
  }
}
