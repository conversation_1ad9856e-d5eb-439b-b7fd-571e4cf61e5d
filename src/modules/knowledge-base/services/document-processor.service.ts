import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface DocumentChunk {
  id: string;
  content: string;
  startIndex: number;
  endIndex: number;
  tokens: number;
}

export interface ProcessingOptions {
  chunkSize: number;
  chunkOverlap: number;
  preserveParagraphs: boolean;
  preserveSentences: boolean;
}

@Injectable()
export class DocumentProcessorService {
  private readonly logger = new Logger(DocumentProcessorService.name);
  private readonly defaultChunkSize: number;
  private readonly defaultChunkOverlap: number;

  constructor(private configService: ConfigService) {
    this.defaultChunkSize = this.configService.get<number>('DEFAULT_CHUNK_SIZE', 1000);
    this.defaultChunkOverlap = this.configService.get<number>('DEFAULT_CHUNK_OVERLAP', 200);
  }

  async processDocument(
    content: string,
    options?: Partial<ProcessingOptions>
  ): Promise<DocumentChunk[]> {
    const processingOptions: ProcessingOptions = {
      chunkSize: options?.chunkSize || this.defaultChunkSize,
      chunkOverlap: options?.chunkOverlap || this.defaultChunkOverlap,
      preserveParagraphs: options?.preserveParagraphs ?? true,
      preserveSentences: options?.preserveSentences ?? true,
    };

    this.logger.debug(`Processing document with ${content.length} characters`);

    // Clean and normalize the content
    const cleanedContent = this.cleanContent(content);

    // Split into chunks
    const chunks = this.splitIntoChunks(cleanedContent, processingOptions);

    this.logger.log(`Document processed into ${chunks.length} chunks`);
    return chunks;
  }

  private cleanContent(content: string): string {
    // Remove excessive whitespace
    let cleaned = content.replace(/\s+/g, ' ');
    
    // Remove special characters that might interfere with processing
    cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    
    // Normalize line breaks
    cleaned = cleaned.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    
    // Remove excessive line breaks
    cleaned = cleaned.replace(/\n{3,}/g, '\n\n');
    
    return cleaned.trim();
  }

  private splitIntoChunks(content: string, options: ProcessingOptions): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    let currentIndex = 0;
    let chunkId = 0;

    while (currentIndex < content.length) {
      const chunkEnd = Math.min(currentIndex + options.chunkSize, content.length);
      let actualChunkEnd = chunkEnd;

      // If we're not at the end of the document, try to find a good break point
      if (chunkEnd < content.length) {
        actualChunkEnd = this.findBreakPoint(
          content,
          currentIndex,
          chunkEnd,
          options
        );
      }

      const chunkContent = content.substring(currentIndex, actualChunkEnd).trim();
      
      if (chunkContent.length > 0) {
        chunks.push({
          id: `chunk_${chunkId++}`,
          content: chunkContent,
          startIndex: currentIndex,
          endIndex: actualChunkEnd,
          tokens: this.estimateTokens(chunkContent),
        });
      }

      // Move to next chunk with overlap
      currentIndex = Math.max(
        actualChunkEnd - options.chunkOverlap,
        currentIndex + 1
      );
    }

    return chunks;
  }

  private findBreakPoint(
    content: string,
    start: number,
    end: number,
    options: ProcessingOptions
  ): number {
    const searchWindow = Math.min(200, Math.floor(options.chunkSize * 0.2));
    const searchStart = Math.max(end - searchWindow, start);

    // Try to break at paragraph boundary
    if (options.preserveParagraphs) {
      const paragraphBreak = content.lastIndexOf('\n\n', end);
      if (paragraphBreak > searchStart) {
        return paragraphBreak + 2;
      }
    }

    // Try to break at sentence boundary
    if (options.preserveSentences) {
      const sentenceBreak = this.findSentenceBreak(content, searchStart, end);
      if (sentenceBreak > searchStart) {
        return sentenceBreak;
      }
    }

    // Try to break at word boundary
    const wordBreak = content.lastIndexOf(' ', end);
    if (wordBreak > searchStart) {
      return wordBreak + 1;
    }

    // If no good break point found, use the original end
    return end;
  }

  private findSentenceBreak(content: string, start: number, end: number): number {
    const sentenceEnders = /[.!?]\s+/g;
    let match;
    let lastMatch = -1;

    sentenceEnders.lastIndex = start;
    
    while ((match = sentenceEnders.exec(content)) !== null && match.index < end) {
      lastMatch = match.index + match[0].length;
    }

    return lastMatch;
  }

  private estimateTokens(text: string): number {
    // Simple token estimation: approximately 1 token per 4 characters
    // This is a rough approximation and could be improved with a proper tokenizer
    return Math.ceil(text.length / 4);
  }

  async extractMetadata(content: string): Promise<{
    wordCount: number;
    characterCount: number;
    estimatedTokens: number;
    language?: string;
    readingTime: number; // in minutes
  }> {
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
    const characterCount = content.length;
    const estimatedTokens = this.estimateTokens(content);
    
    // Estimate reading time (average 200 words per minute)
    const readingTime = Math.ceil(wordCount / 200);

    // Simple language detection (could be improved with a proper library)
    const language = this.detectLanguage(content);

    return {
      wordCount,
      characterCount,
      estimatedTokens,
      language,
      readingTime,
    };
  }

  private detectLanguage(content: string): string {
    // Very basic language detection
    // In a real implementation, you might use a library like franc or langdetect
    
    const sample = content.substring(0, 1000).toLowerCase();
    
    // Check for common English words
    const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const englishCount = englishWords.reduce((count, word) => {
      return count + (sample.split(word).length - 1);
    }, 0);

    if (englishCount > 5) {
      return 'en';
    }

    return 'unknown';
  }

  validateChunkSize(chunkSize: number): boolean {
    return chunkSize >= 100 && chunkSize <= 4000;
  }

  validateChunkOverlap(chunkOverlap: number, chunkSize: number): boolean {
    return chunkOverlap >= 0 && chunkOverlap < chunkSize * 0.5;
  }
}
