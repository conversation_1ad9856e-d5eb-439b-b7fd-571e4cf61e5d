import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

export interface EmbeddingResult {
  embedding: number[];
  tokens: number;
  model: string;
}

@Injectable()
export class EmbeddingService {
  private readonly logger = new Logger(EmbeddingService.name);
  private readonly openai: OpenAI;
  private readonly embeddingModel: string;
  private readonly maxTokens: number;

  constructor(private configService: ConfigService) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
    
    this.embeddingModel = this.configService.get<string>('EMBEDDING_MODEL', 'text-embedding-3-small');
    this.maxTokens = this.configService.get<number>('EMBEDDING_MAX_TOKENS', 8191);
  }

  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    try {
      // Truncate text if it's too long
      const truncatedText = this.truncateText(text, this.maxTokens);
      
      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: truncatedText,
      });

      const embedding = response.data[0];
      
      return {
        embedding: embedding.embedding,
        tokens: response.usage.total_tokens,
        model: this.embeddingModel,
      };
    } catch (error) {
      this.logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  async generateBatchEmbeddings(texts: string[]): Promise<EmbeddingResult[]> {
    try {
      // Process in batches to avoid API limits
      const batchSize = 100;
      const results: EmbeddingResult[] = [];

      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const truncatedBatch = batch.map(text => this.truncateText(text, this.maxTokens));

        const response = await this.openai.embeddings.create({
          model: this.embeddingModel,
          input: truncatedBatch,
        });

        const batchResults = response.data.map((embedding, index) => ({
          embedding: embedding.embedding,
          tokens: Math.floor(response.usage.total_tokens / batch.length), // Approximate
          model: this.embeddingModel,
        }));

        results.push(...batchResults);
        
        // Add delay between batches to respect rate limits
        if (i + batchSize < texts.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      this.logger.log(`Generated ${results.length} embeddings`);
      return results;
    } catch (error) {
      this.logger.error('Error generating batch embeddings:', error);
      throw error;
    }
  }

  async calculateSimilarity(embedding1: number[], embedding2: number[]): Promise<number> {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions');
    }

    // Calculate cosine similarity
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    return similarity;
  }

  private truncateText(text: string, maxTokens: number): string {
    // Simple approximation: 1 token ≈ 4 characters
    const maxChars = maxTokens * 4;
    
    if (text.length <= maxChars) {
      return text;
    }

    // Truncate at word boundary
    const truncated = text.substring(0, maxChars);
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    
    return lastSpaceIndex > 0 ? truncated.substring(0, lastSpaceIndex) : truncated;
  }

  getEmbeddingDimensions(): number {
    // Return dimensions based on model
    switch (this.embeddingModel) {
      case 'text-embedding-3-small':
        return 1536;
      case 'text-embedding-3-large':
        return 3072;
      case 'text-embedding-ada-002':
        return 1536;
      default:
        return 1536;
    }
  }

  getMaxTokens(): number {
    return this.maxTokens;
  }

  getModel(): string {
    return this.embeddingModel;
  }
}
