import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Pinecone } from '@pinecone-database/pinecone';
import { 
  IVectorStore, 
  VectorEmbedding, 
  VectorSearchResult, 
  VectorSearchOptions 
} from '../../interfaces/vector-store.interface';

@Injectable()
export class PineconeService implements IVectorStore, OnModuleInit {
  private readonly logger = new Logger(PineconeService.name);
  private pinecone: Pinecone;
  private index: any;
  private readonly indexName: string;
  private readonly environment: string;
  private readonly dimensions: number;

  constructor(private configService: ConfigService) {
    this.indexName = this.configService.get<string>('PINECONE_INDEX_NAME', 'ai-knowledge-base');
    this.environment = this.configService.get<string>('PINECONE_ENVIRONMENT', 'us-east-1-aws');
    this.dimensions = this.configService.get<number>('PINECONE_DIMENSIONS', 1536);
  }

  async onModuleInit() {
    await this.initialize();
  }

  async initialize(): Promise<void> {
    try {
      this.pinecone = new Pinecone({
        apiKey: this.configService.get<string>('PINECONE_API_KEY'),
      });

      // Get or create index
      this.index = this.pinecone.index(this.indexName);
      
      this.logger.log(`Pinecone initialized with index: ${this.indexName}`);
    } catch (error) {
      this.logger.error('Failed to initialize Pinecone:', error);
      throw error;
    }
  }

  async upsert(embeddings: VectorEmbedding[]): Promise<void> {
    try {
      const vectors = embeddings.map(embedding => ({
        id: embedding.id,
        values: embedding.vector,
        metadata: embedding.metadata,
      }));

      // Upsert in batches of 100 (Pinecone limit)
      const batchSize = 100;
      for (let i = 0; i < vectors.length; i += batchSize) {
        const batch = vectors.slice(i, i + batchSize);
        await this.index.upsert(batch);
      }

      this.logger.log(`Upserted ${embeddings.length} vectors to Pinecone`);
    } catch (error) {
      this.logger.error('Error upserting vectors to Pinecone:', error);
      throw error;
    }
  }

  async search(
    queryVector: number[], 
    options: VectorSearchOptions
  ): Promise<VectorSearchResult[]> {
    try {
      const queryRequest: any = {
        vector: queryVector,
        topK: options.topK,
        includeMetadata: options.includeMetadata ?? true,
        includeValues: options.includeValues ?? false,
      };

      if (options.filter) {
        queryRequest.filter = options.filter;
      }

      const response = await this.index.query(queryRequest);
      
      const results: VectorSearchResult[] = response.matches
        .filter(match => !options.threshold || match.score >= options.threshold)
        .map(match => ({
          id: match.id,
          score: match.score,
          metadata: match.metadata || {},
        }));

      this.logger.debug(`Pinecone search returned ${results.length} results`);
      return results;
    } catch (error) {
      this.logger.error('Error searching Pinecone:', error);
      throw error;
    }
  }

  async delete(ids: string[]): Promise<void> {
    try {
      await this.index.deleteMany(ids);
      this.logger.log(`Deleted ${ids.length} vectors from Pinecone`);
    } catch (error) {
      this.logger.error('Error deleting vectors from Pinecone:', error);
      throw error;
    }
  }

  async deleteByFilter(filter: Record<string, any>): Promise<void> {
    try {
      await this.index.deleteMany({ filter });
      this.logger.log('Deleted vectors by filter from Pinecone');
    } catch (error) {
      this.logger.error('Error deleting vectors by filter from Pinecone:', error);
      throw error;
    }
  }

  async getStats(): Promise<{
    totalVectors: number;
    dimensions: number;
    indexSize: number;
  }> {
    try {
      const stats = await this.index.describeIndexStats();
      
      return {
        totalVectors: stats.totalVectorCount || 0,
        dimensions: this.dimensions,
        indexSize: stats.indexFullness || 0,
      };
    } catch (error) {
      this.logger.error('Error getting Pinecone stats:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.index.describeIndexStats();
      return true;
    } catch (error) {
      this.logger.error('Pinecone health check failed:', error);
      return false;
    }
  }

  async createIndex(): Promise<void> {
    try {
      await this.pinecone.createIndex({
        name: this.indexName,
        dimension: this.dimensions,
        metric: 'cosine',
        spec: {
          serverless: {
            cloud: 'aws',
            region: 'us-east-1',
          },
        },
      });

      this.logger.log(`Created Pinecone index: ${this.indexName}`);
    } catch (error) {
      this.logger.error('Error creating Pinecone index:', error);
      throw error;
    }
  }

  async deleteIndex(): Promise<void> {
    try {
      await this.pinecone.deleteIndex(this.indexName);
      this.logger.log(`Deleted Pinecone index: ${this.indexName}`);
    } catch (error) {
      this.logger.error('Error deleting Pinecone index:', error);
      throw error;
    }
  }
}
