import { 
  Controller, 
  Post, 
  Get, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards, 
  Req,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { Request } from 'express';

import { KnowledgeBaseService } from '../services/knowledge-base.service';
import { 
  UploadDocumentDto, 
  SearchKnowledgeBaseDto, 
  KnowledgeBaseSearchResponse,
  ProcessDocumentDto 
} from '../dto/knowledge-base.dto';
import { KnowledgeBase, DocumentType } from '../../../entities';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@ApiTags('Knowledge Base')
@Controller('knowledge-base')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a document to the knowledge base' })
  @ApiResponse({ 
    status: 201, 
    description: 'Document uploaded successfully',
    type: KnowledgeBase,
  })
  @ApiResponse({ status: 400, description: 'Invalid document data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async uploadDocument(
    @Req() req: Request,
    @Body() uploadDto: UploadDocumentDto,
  ): Promise<KnowledgeBase> {
    const userId = req.user?.id;
    return await this.knowledgeBaseService.uploadDocument(userId, uploadDto);
  }

  @Post('upload-file')
  @ApiOperation({ summary: 'Upload a file to the knowledge base' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ 
    status: 201, 
    description: 'File uploaded successfully',
    type: KnowledgeBase,
  })
  async uploadFile(
    @Req() req: Request,
    @UploadedFile() file: Express.Multer.File,
    @Body('title') title: string,
    @Body('type') type: DocumentType,
    @Body('summary') summary?: string,
  ): Promise<KnowledgeBase> {
    const userId = req.user?.id;
    
    if (!file) {
      throw new Error('No file uploaded');
    }

    const content = file.buffer.toString('utf-8');
    
    const uploadDto: UploadDocumentDto = {
      title: title || file.originalname,
      content,
      type,
      summary,
      metadata: {
        source: 'file_upload',
        filename: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype,
      },
    };

    return await this.knowledgeBaseService.uploadDocument(userId, uploadDto);
  }

  @Post('search')
  @ApiOperation({ summary: 'Search the knowledge base' })
  @ApiResponse({ 
    status: 200, 
    description: 'Search results',
    type: KnowledgeBaseSearchResponse,
  })
  async searchKnowledgeBase(
    @Body() searchDto: SearchKnowledgeBaseDto,
  ): Promise<KnowledgeBaseSearchResponse> {
    return await this.knowledgeBaseService.searchKnowledgeBase(searchDto);
  }

  @Post('process/:id')
  @ApiOperation({ summary: 'Process a document for indexing' })
  @ApiResponse({ 
    status: 200, 
    description: 'Document processing started',
  })
  async processDocument(
    @Param('id') documentId: string,
    @Body() processDto: Omit<ProcessDocumentDto, 'documentId'>,
  ): Promise<{ message: string }> {
    await this.knowledgeBaseService.processDocument({
      documentId,
      ...processDto,
    });
    
    return { message: 'Document processing started' };
  }

  @Get('documents')
  @ApiOperation({ summary: 'Get all documents' })
  @ApiResponse({ 
    status: 200, 
    description: 'List of documents',
  })
  async getDocuments(
    @Query('type') type?: DocumentType,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<{
    documents: KnowledgeBase[];
    total: number;
  }> {
    if (type) {
      const documents = await this.knowledgeBaseService.getDocumentsByType(type);
      return {
        documents: documents.slice(offset, offset + limit),
        total: documents.length,
      };
    }

    // Implementation for getting all documents with pagination
    return {
      documents: [],
      total: 0,
    };
  }

  @Get('documents/:id')
  @ApiOperation({ summary: 'Get a specific document' })
  @ApiResponse({ 
    status: 200, 
    description: 'Document details',
    type: KnowledgeBase,
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async getDocument(
    @Param('id') id: string,
  ): Promise<KnowledgeBase> {
    return await this.knowledgeBaseService.getDocument(id);
  }

  @Delete('documents/:id')
  @ApiOperation({ summary: 'Delete a document' })
  @ApiResponse({ 
    status: 200, 
    description: 'Document deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async deleteDocument(
    @Param('id') id: string,
  ): Promise<{ message: string }> {
    await this.knowledgeBaseService.deleteDocument(id);
    return { message: 'Document deleted successfully' };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get knowledge base statistics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Knowledge base statistics',
  })
  async getStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<DocumentType, number>;
    documentsByStatus: Record<string, number>;
    totalTokens: number;
    vectorStoreStats: any;
  }> {
    return await this.knowledgeBaseService.getStats();
  }

  @Get('types')
  @ApiOperation({ summary: 'Get available document types' })
  @ApiResponse({ 
    status: 200, 
    description: 'Available document types',
  })
  async getDocumentTypes(): Promise<{
    types: Array<{
      value: DocumentType;
      label: string;
      description: string;
    }>;
  }> {
    return {
      types: [
        {
          value: DocumentType.API_DOCUMENTATION,
          label: 'API Documentation',
          description: 'Documentation for API endpoints and usage',
        },
        {
          value: DocumentType.USER_MANUAL,
          label: 'User Manual',
          description: 'User guides and manuals',
        },
        {
          value: DocumentType.FAQ,
          label: 'FAQ',
          description: 'Frequently asked questions',
        },
        {
          value: DocumentType.KNOWLEDGE_ARTICLE,
          label: 'Knowledge Article',
          description: 'General knowledge articles',
        },
        {
          value: DocumentType.CODE_SNIPPET,
          label: 'Code Snippet',
          description: 'Code examples and snippets',
        },
        {
          value: DocumentType.TUTORIAL,
          label: 'Tutorial',
          description: 'Step-by-step tutorials',
        },
      ],
    };
  }
}
