import { Injectable, Inject } from '@nestjs/common';
import * as OktaJwtVerifier from '@okta/jwt-verifier';
import { IToken } from './interfaces/token.interface';
import { AppLogger } from '../../core/logger/logger.service';
import { MWHttpService } from '../../core/service/http.service';
import { audience } from '../../utils/const';

@Injectable()
export class AuthService implements IToken{

    constructor(private readonly httpService: MWHttpService, private readonly appLogger: AppLogger) { }

    async validateToken(token: string): Promise<OktaJwtVerifier.Jwt> {
        this.appLogger.log("Validate OKTA Token:", token);
        const oktaJwtVerifier = new OktaJwtVerifier({
            issuer: process.env.OKTA_INSTANCE as string, // issuer required
            clientId: process.env.PUSH_OKTA_CLIENT_ID as string,
        });
        try {
            const jwt = await oktaJwtVerifier.verifyAccessToken(token, audience);
            return jwt;
        } catch (error) {
            this.appLogger.log("Error validating token:", error);
            throw error;
        }
    }
}
