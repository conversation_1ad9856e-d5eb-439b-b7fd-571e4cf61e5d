import { Body, Controller, Post, Res, UnauthorizedException, BadRequestException, UseInterceptors } from '@nestjs/common';
import { TokenRequest } from './dto/token.dto';
import { AuthService } from './auth.service';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';

@Controller({version: '1'})
@UseInterceptors(TimeoutInterceptor)
export class AuthController {

    constructor(private authService: AuthService) { }
}
