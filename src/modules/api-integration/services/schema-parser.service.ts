import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

import { HttpMethod, EndpointStatus } from '../../../entities';
import { 
  ParseApiSchemaDto, 
  ApiSchemaParseResultDto, 
  CreateApiEndpointDto 
} from '../dto/api-integration.dto';

interface OpenAPISpec {
  openapi?: string;
  swagger?: string;
  info: {
    title: string;
    version: string;
    description?: string;
  };
  servers?: Array<{
    url: string;
    description?: string;
  }>;
  paths: Record<string, Record<string, any>>;
  components?: {
    schemas?: Record<string, any>;
    securitySchemes?: Record<string, any>;
  };
}

@Injectable()
export class SchemaParserService {
  private readonly logger = new Logger(SchemaParserService.name);

  constructor() {}

  async parseApiSchema(
    userId: string,
    parseDto: ParseApiSchemaDto,
  ): Promise<ApiSchemaParseResultDto> {
    try {
      let spec: OpenAPISpec;
      
      // Parse the schema based on format
      switch (parseDto.format) {
        case 'openapi':
        case 'swagger':
          spec = JSON.parse(parseDto.schema);
          break;
        case 'postman':
          spec = this.convertPostmanToOpenAPI(JSON.parse(parseDto.schema));
          break;
        default:
          throw new BadRequestException(`Unsupported schema format: ${parseDto.format}`);
      }

      // Validate the parsed spec
      this.validateOpenAPISpec(spec);

      // Extract endpoints
      const endpoints = this.extractEndpoints(spec, parseDto.baseUrl);
      
      // Filter endpoints based on options
      const filteredEndpoints = this.filterEndpoints(endpoints, parseDto.options);

      const result: ApiSchemaParseResultDto = {
        endpointsCount: filteredEndpoints.length,
        endpoints: filteredEndpoints,
        metadata: {
          title: spec.info.title,
          version: spec.info.version,
          description: spec.info.description,
          baseUrl: parseDto.baseUrl || this.extractBaseUrl(spec),
        },
        warnings: [],
        errors: [],
      };

      this.logger.log(`Parsed ${result.endpointsCount} endpoints from ${parseDto.format} schema`);
      return result;

    } catch (error) {
      this.logger.error('Error parsing API schema:', error);
      throw new BadRequestException(`Failed to parse schema: ${error.message}`);
    }
  }

  async importEndpoints(
    userId: string,
    parseResult: ApiSchemaParseResultDto,
  ): Promise<any[]> {
    const importedEndpoints: any[] = [];

    try {
      for (const endpointData of parseResult.endpoints) {
        const createDto: CreateApiEndpointDto = {
          name: endpointData.name,
          description: endpointData.description,
          method: endpointData.method,
          path: endpointData.path,
          baseUrl: parseResult.metadata.baseUrl || '',
          status: EndpointStatus.ACTIVE,
          requestSchema: this.extractRequestSchema(endpointData.parameters),
          responseSchema: this.extractResponseSchema(endpointData.responses),
          authentication: this.extractAuthenticationInfo(endpointData),
          metadata: {
            tags: endpointData.tags,
            version: parseResult.metadata.version,
          },
        };

        const endpoint = await this.createEndpoint(userId, createDto);
        importedEndpoints.push(endpoint);
      }

      this.logger.log(`Imported ${importedEndpoints.length} endpoints`);
      return importedEndpoints;

    } catch (error) {
      this.logger.error('Error importing endpoints:', error);
      throw error;
    }
  }

  private validateOpenAPISpec(spec: OpenAPISpec): void {
    if (!spec.info) {
      throw new BadRequestException('Missing info section in API spec');
    }

    if (!spec.info.title || !spec.info.version) {
      throw new BadRequestException('Missing title or version in API spec info');
    }

    if (!spec.paths || Object.keys(spec.paths).length === 0) {
      throw new BadRequestException('No paths found in API spec');
    }
  }

  private extractEndpoints(spec: OpenAPISpec, baseUrlOverride?: string): any[] {
    const endpoints: any[] = [];
    const baseUrl = baseUrlOverride || this.extractBaseUrl(spec);

    Object.entries(spec.paths).forEach(([path, pathItem]) => {
      Object.entries(pathItem).forEach(([method, operation]) => {
        if (!this.isValidHttpMethod(method.toUpperCase())) {
          return;
        }

        const endpoint = {
          name: operation.operationId || `${method.toUpperCase()} ${path}`,
          description: operation.summary || operation.description,
          method: method.toUpperCase() as HttpMethod,
          path,
          baseUrl,
          tags: operation.tags || [],
          parameters: operation.parameters || [],
          responses: operation.responses || {},
          security: operation.security,
        };

        endpoints.push(endpoint);
      });
    });

    return endpoints;
  }

  private extractBaseUrl(spec: OpenAPISpec): string {
    if (spec.servers && spec.servers.length > 0) {
      return spec.servers[0].url;
    }

    // Fallback for Swagger 2.0
    if (spec.swagger && (spec as any).host) {
      const scheme = (spec as any).schemes?.[0] || 'https';
      const basePath = (spec as any).basePath || '';
      return `${scheme}://${(spec as any).host}${basePath}`;
    }

    return '';
  }

  private filterEndpoints(endpoints: any[], options?: any): any[] {
    if (!options) {
      return endpoints;
    }

    let filtered = endpoints;

    // Filter by tags
    if (options.filterTags && options.filterTags.length > 0) {
      filtered = filtered.filter(endpoint => 
        endpoint.tags.some(tag => options.filterTags.includes(tag))
      );
    }

    // Filter by paths
    if (options.filterPaths && options.filterPaths.length > 0) {
      filtered = filtered.filter(endpoint =>
        options.filterPaths.some(pathPattern => 
          endpoint.path.includes(pathPattern)
        )
      );
    }

    // Filter deprecated endpoints
    if (!options.includeDeprecated) {
      filtered = filtered.filter(endpoint => !endpoint.deprecated);
    }

    return filtered;
  }

  private extractRequestSchema(parameters: any[]): any {
    const schema = {
      headers: {},
      queryParams: {},
      pathParams: {},
      body: {},
    };

    parameters.forEach(param => {
      switch (param.in) {
        case 'header':
          schema.headers[param.name] = {
            type: param.type || param.schema?.type,
            required: param.required,
            description: param.description,
          };
          break;
        case 'query':
          schema.queryParams[param.name] = {
            type: param.type || param.schema?.type,
            required: param.required,
            description: param.description,
          };
          break;
        case 'path':
          schema.pathParams[param.name] = {
            type: param.type || param.schema?.type,
            required: true,
            description: param.description,
          };
          break;
        case 'body':
          schema.body = param.schema || {};
          break;
      }
    });

    return schema;
  }

  private extractResponseSchema(responses: any): any {
    const schema = {
      success: {},
      error: {},
      examples: [],
    };

    Object.entries(responses).forEach(([statusCode, response]: [string, any]) => {
      const status = parseInt(statusCode);
      
      if (status >= 200 && status < 300) {
        schema.success[statusCode] = {
          description: response.description,
          schema: response.schema || response.content,
        };
      } else {
        schema.error[statusCode] = {
          description: response.description,
          schema: response.schema || response.content,
        };
      }

      // Add examples
      if (response.examples) {
        Object.entries(response.examples).forEach(([name, example]: [string, any]) => {
          schema.examples.push({
            status,
            description: name,
            body: example.value || example,
          });
        });
      }
    });

    return schema;
  }

  private extractAuthenticationInfo(endpoint: any): any {
    if (!endpoint.security || endpoint.security.length === 0) {
      return { type: 'none', required: false };
    }

    // Simple authentication detection
    const firstSecurity = endpoint.security[0];
    const securityName = Object.keys(firstSecurity)[0];

    // This is a simplified implementation
    // In a real scenario, you'd need to check the security schemes
    if (securityName.toLowerCase().includes('bearer')) {
      return { type: 'bearer', required: true };
    } else if (securityName.toLowerCase().includes('basic')) {
      return { type: 'basic', required: true };
    } else if (securityName.toLowerCase().includes('api')) {
      return { type: 'apikey', required: true };
    }

    return { type: 'bearer', required: true };
  }

  private convertPostmanToOpenAPI(postmanCollection: any): OpenAPISpec {
    // Basic Postman to OpenAPI conversion
    // This is a simplified implementation
    const spec: OpenAPISpec = {
      openapi: '3.0.0',
      info: {
        title: postmanCollection.info?.name || 'Imported API',
        version: '1.0.0',
        description: postmanCollection.info?.description,
      },
      paths: {},
    };

    // Convert Postman items to OpenAPI paths
    if (postmanCollection.item) {
      this.convertPostmanItems(postmanCollection.item, spec.paths);
    }

    return spec;
  }

  private convertPostmanItems(items: any[], paths: Record<string, any>): void {
    items.forEach(item => {
      if (item.request) {
        const method = item.request.method.toLowerCase();
        const url = item.request.url;
        const path = typeof url === 'string' ? url : url.path?.join('/') || '/';

        if (!paths[path]) {
          paths[path] = {};
        }

        paths[path][method] = {
          summary: item.name,
          description: item.request.description,
          parameters: [],
          responses: {
            '200': {
              description: 'Successful response',
            },
          },
        };
      }

      // Handle nested items (folders)
      if (item.item) {
        this.convertPostmanItems(item.item, paths);
      }
    });
  }

  private isValidHttpMethod(method: string): boolean {
    return Object.values(HttpMethod).includes(method as HttpMethod);
  }

  private async createEndpoint(userId: string, createDto: CreateApiEndpointDto): Promise<any> {
    // Return mock endpoint for simplified version
    this.logger.log('Create endpoint called - returning mock endpoint');
    return {
      id: uuidv4(),
      ...createDto,
      createdById: userId,
      callCount: 0,
      successCount: 0,
      errorCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }
}
