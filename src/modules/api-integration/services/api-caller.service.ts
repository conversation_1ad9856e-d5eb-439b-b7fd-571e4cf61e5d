import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import Redis from 'ioredis';

import { ApiEndpoint, EndpointStatus } from '../../../entities';
import { ApiCallRequestDto, ApiCallResponseDto } from '../dto/api-integration.dto';

@Injectable()
export class ApiCallerService {
  private readonly logger = new Logger(ApiCallerService.name);
  private readonly redis: Redis;
  private readonly defaultTimeout: number;
  private readonly enableCaching: boolean;
  private readonly defaultCacheTtl: number;

  constructor(
    @InjectRepository(ApiEndpoint)
    private apiEndpointRepository: Repository<ApiEndpoint>,
    private httpService: HttpService,
    private configService: ConfigService,
  ) {
    this.redis = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 1), // Different DB for API cache
    });

    this.defaultTimeout = this.configService.get<number>('API_DEFAULT_TIMEOUT', 30000);
    this.enableCaching = this.configService.get<boolean>('API_ENABLE_CACHING', true);
    this.defaultCacheTtl = this.configService.get<number>('API_DEFAULT_CACHE_TTL', 300); // 5 minutes
  }

  async callApi(request: ApiCallRequestDto): Promise<ApiCallResponseDto> {
    const startTime = Date.now();

    try {
      // Get endpoint configuration
      const endpoint = await this.getEndpoint(request.endpointId);
      
      // Validate endpoint status
      if (endpoint.status !== EndpointStatus.ACTIVE) {
        throw new BadRequestException(`Endpoint is ${endpoint.status}`);
      }

      // Check cache if enabled
      if (request.enableCaching !== false && this.enableCaching && endpoint.method === 'GET') {
        const cachedResponse = await this.getCachedResponse(request);
        if (cachedResponse) {
          return {
            ...cachedResponse,
            cache: { hit: true, ttl: await this.getCacheTtl(request) },
          };
        }
      }

      // Build request configuration
      const requestConfig = await this.buildRequestConfig(endpoint, request);
      
      // Execute API call
      const response = await this.executeRequest(requestConfig);
      
      // Update endpoint usage statistics
      await this.updateEndpointStats(endpoint.id, true, Date.now() - startTime);

      // Build response
      const apiResponse: ApiCallResponseDto = {
        success: true,
        statusCode: response.status,
        data: response.data,
        headers: response.headers as Record<string, string>,
        executionTime: Date.now() - startTime,
        endpoint: {
          id: endpoint.id,
          name: endpoint.name,
          method: endpoint.method,
          path: endpoint.path,
          baseUrl: endpoint.baseUrl,
        },
      };

      // Cache response if enabled
      if (request.enableCaching !== false && this.enableCaching && endpoint.method === 'GET') {
        await this.cacheResponse(request, apiResponse, request.cacheTtl || this.defaultCacheTtl);
      }

      this.logger.log(`API call successful: ${endpoint.method} ${endpoint.path}`);
      return apiResponse;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Update endpoint error statistics
      const endpoint = await this.getEndpoint(request.endpointId);
      await this.updateEndpointStats(endpoint.id, false, executionTime);

      this.logger.error(`API call failed: ${error.message}`);
      
      return {
        success: false,
        statusCode: error.response?.status || 500,
        data: null,
        error: error.message,
        headers: error.response?.headers || {},
        executionTime,
        endpoint: {
          id: endpoint.id,
          name: endpoint.name,
          method: endpoint.method,
          path: endpoint.path,
          baseUrl: endpoint.baseUrl,
        },
      };
    }
  }

  private async getEndpoint(endpointId: string): Promise<ApiEndpoint> {
    const endpoint = await this.apiEndpointRepository.findOne({
      where: { id: endpointId },
    });

    if (!endpoint) {
      throw new NotFoundException(`API endpoint not found: ${endpointId}`);
    }

    return endpoint;
  }

  private async buildRequestConfig(
    endpoint: ApiEndpoint,
    request: ApiCallRequestDto,
  ): Promise<AxiosRequestConfig> {
    // Build URL with path parameters
    let url = endpoint.baseUrl + endpoint.path;
    if (request.parameters?.pathParams) {
      Object.entries(request.parameters.pathParams).forEach(([key, value]) => {
        url = url.replace(`{${key}}`, encodeURIComponent(String(value)));
      });
    }

    const config: AxiosRequestConfig = {
      method: endpoint.method.toLowerCase() as any,
      url,
      timeout: request.timeout || this.defaultTimeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Backend-Service/1.0',
        ...request.parameters?.headers,
      },
    };

    // Add query parameters
    if (request.parameters?.queryParams) {
      config.params = request.parameters.queryParams;
    }

    // Add request body
    if (request.parameters?.body && ['POST', 'PUT', 'PATCH'].includes(endpoint.method)) {
      config.data = request.parameters.body;
    }

    // Add authentication
    await this.addAuthentication(config, endpoint, request.authentication);

    return config;
  }

  private async addAuthentication(
    config: AxiosRequestConfig,
    endpoint: ApiEndpoint,
    authOverride?: any,
  ): Promise<void> {
    const auth = authOverride || endpoint.authentication;
    
    if (!auth || !auth.required) {
      return;
    }

    switch (auth.type) {
      case 'bearer':
        const token = authOverride?.credentials?.token || 
                     this.configService.get<string>('API_BEARER_TOKEN');
        if (token) {
          config.headers!['Authorization'] = `Bearer ${token}`;
        }
        break;

      case 'basic':
        const username = authOverride?.credentials?.username || 
                        this.configService.get<string>('API_BASIC_USERNAME');
        const password = authOverride?.credentials?.password || 
                        this.configService.get<string>('API_BASIC_PASSWORD');
        if (username && password) {
          const credentials = Buffer.from(`${username}:${password}`).toString('base64');
          config.headers!['Authorization'] = `Basic ${credentials}`;
        }
        break;

      case 'apikey':
        const apiKey = authOverride?.credentials?.apiKey || 
                      this.configService.get<string>('API_KEY');
        const headerName = authOverride?.credentials?.headerName || 'X-API-Key';
        if (apiKey) {
          config.headers![headerName] = apiKey;
        }
        break;

      case 'oauth2':
        // OAuth2 implementation would go here
        this.logger.warn('OAuth2 authentication not yet implemented');
        break;
    }
  }

  private async executeRequest(config: AxiosRequestConfig): Promise<AxiosResponse> {
    try {
      return await firstValueFrom(this.httpService.request(config));
    } catch (error) {
      if (error.response) {
        // HTTP error response
        throw error;
      } else if (error.request) {
        // Network error
        throw new Error(`Network error: ${error.message}`);
      } else {
        // Request configuration error
        throw new Error(`Request error: ${error.message}`);
      }
    }
  }

  private async updateEndpointStats(
    endpointId: string,
    success: boolean,
    responseTime: number,
  ): Promise<void> {
    try {
      const endpoint = await this.apiEndpointRepository.findOne({
        where: { id: endpointId },
      });

      if (!endpoint) return;

      const updates: Partial<ApiEndpoint> = {
        callCount: endpoint.callCount + 1,
        lastUsedAt: new Date(),
      };

      if (success) {
        updates.successCount = endpoint.successCount + 1;
      } else {
        updates.errorCount = endpoint.errorCount + 1;
      }

      // Update average response time
      const totalCalls = endpoint.callCount + 1;
      const currentAvg = endpoint.averageResponseTime || 0;
      updates.averageResponseTime = ((currentAvg * endpoint.callCount) + responseTime) / totalCalls;

      await this.apiEndpointRepository.update(endpointId, updates);
    } catch (error) {
      this.logger.error(`Error updating endpoint stats: ${error.message}`);
    }
  }

  private async getCachedResponse(request: ApiCallRequestDto): Promise<ApiCallResponseDto | null> {
    try {
      const cacheKey = this.buildCacheKey(request);
      const cached = await this.redis.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      this.logger.error(`Error getting cached response: ${error.message}`);
    }
    
    return null;
  }

  private async cacheResponse(
    request: ApiCallRequestDto,
    response: ApiCallResponseDto,
    ttl: number,
  ): Promise<void> {
    try {
      const cacheKey = this.buildCacheKey(request);
      await this.redis.setex(cacheKey, ttl, JSON.stringify(response));
    } catch (error) {
      this.logger.error(`Error caching response: ${error.message}`);
    }
  }

  private async getCacheTtl(request: ApiCallRequestDto): Promise<number> {
    try {
      const cacheKey = this.buildCacheKey(request);
      return await this.redis.ttl(cacheKey);
    } catch (error) {
      return 0;
    }
  }

  private buildCacheKey(request: ApiCallRequestDto): string {
    const keyData = {
      endpointId: request.endpointId,
      parameters: request.parameters,
    };
    
    const keyString = JSON.stringify(keyData);
    return `api_cache:${Buffer.from(keyString).toString('base64')}`;
  }

  async clearCache(endpointId?: string): Promise<void> {
    try {
      if (endpointId) {
        const pattern = `api_cache:*${endpointId}*`;
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      } else {
        const keys = await this.redis.keys('api_cache:*');
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
      
      this.logger.log(`Cache cleared for ${endpointId || 'all endpoints'}`);
    } catch (error) {
      this.logger.error(`Error clearing cache: ${error.message}`);
    }
  }
}
