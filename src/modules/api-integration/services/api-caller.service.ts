import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';
import { ApiCallRequestDto, ApiCallResponseDto } from '../dto/api-integration.dto';

@Injectable()
export class ApiCallerService {
  private readonly logger = new Logger(ApiCallerService.name);
  private readonly defaultTimeout: number;
  private readonly cacheStore: Map<string, { data: any, timestamp: number }> = new Map();
  private readonly cacheTtl: number;

  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
  ) {
    this.defaultTimeout = this.configService.get<number>('API_DEFAULT_TIMEOUT', 30000);
    this.cacheTtl = this.configService.get<number>('API_DEFAULT_CACHE_TTL', 300) * 1000; // Convert to milliseconds
  }

  // Simplified API call method for AI function calling
  async callApi(request: ApiCallRequestDto): Promise<ApiCallResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.debug(`Making API call: ${request.method} ${request.url}`);
      
      // Build request configuration
      const requestConfig: any = {
        method: request.method,
        url: request.url,
        headers: request.headers || {},
        timeout: request.timeout || this.defaultTimeout,
      };

      if (request.data) {
        requestConfig.data = request.data;
      }

      if (request.params) {
        requestConfig.params = request.params;
      }

      // Execute API call
      const response = await firstValueFrom(this.httpService.request(requestConfig));
      
      // Build response
      const apiResponse: ApiCallResponseDto = {
        success: true,
        statusCode: response.status,
        data: response.data,
        headers: response.headers as Record<string, string>,
        executionTime: Date.now() - startTime,
      };

      this.logger.debug(`API call completed: ${response.status} in ${apiResponse.executionTime}ms`);
      return apiResponse;
    } catch (error) {
      this.logger.error(`API call failed: ${error.message}`);
      
      const errorResponse: ApiCallResponseDto = {
        success: false,
        statusCode: error.response?.status || 500,
        data: error.response?.data || { error: error.message },
        headers: error.response?.headers || {},
        executionTime: Date.now() - startTime,
        error: {
          message: error.message,
          code: error.code,
          stack: error.stack,
        },
      };

      return errorResponse;
    }
  }

  // Simplified method for making direct HTTP calls
  async makeHttpCall(
    method: string,
    url: string,
    options: {
      headers?: Record<string, string>;
      data?: any;
      params?: Record<string, any>;
      timeout?: number;
    } = {}
  ): Promise<any> {
    try {
      const requestConfig: any = {
        method: method as any,
        url,
        headers: options.headers || {},
        timeout: options.timeout || this.defaultTimeout,
      };

      if (options.data) {
        requestConfig.data = options.data;
      }

      if (options.params) {
        requestConfig.params = options.params;
      }

      const response = await firstValueFrom(this.httpService.request(requestConfig));
      return response.data;
    } catch (error) {
      this.logger.error(`HTTP call failed: ${error.message}`);
      throw error;
    }
  }

  // Clear cache method for controller compatibility
  async clearCache(endpointId?: string): Promise<void> {
    if (endpointId) {
      // Clear cache for specific endpoint
      const keysToDelete = Array.from(this.cacheStore.keys()).filter(key => key.includes(endpointId));
      keysToDelete.forEach(key => this.cacheStore.delete(key));
      this.logger.log(`Cleared cache for endpoint: ${endpointId}`);
    } else {
      // Clear all cache
      this.cacheStore.clear();
      this.logger.log('Cleared all cache');
    }
  }
}
