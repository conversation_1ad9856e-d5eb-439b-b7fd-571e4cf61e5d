import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosRequestConfig } from 'axios';
import Redis from 'ioredis';
import { ApiCallRequestDto, ApiCallResponseDto } from '../dto/api-integration.dto';

@Injectable()
export class ApiCallerService {
  private readonly logger = new Logger(ApiCallerService.name);
  private readonly redis: Redis;
  private readonly defaultTimeout: number;

  constructor(
    private httpService: HttpService,
    private configService: ConfigService,
  ) {
    this.redis = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 1),
    });

    this.defaultTimeout = this.configService.get<number>('API_DEFAULT_TIMEOUT', 30000);
  }

  // Simplified API call method for AI function calling
  async callApi(request: ApiCallRequestDto): Promise<ApiCallResponseDto> {
    const startTime = Date.now();

    try {
      this.logger.debug(`Making API call: ${request.method} ${request.url}`);
      
      // Build request configuration
      const requestConfig: AxiosRequestConfig = {
        method: request.method,
        url: request.url,
        headers: request.headers || {},
        timeout: request.timeout || this.defaultTimeout,
      };

      if (request.data) {
        requestConfig.data = request.data;
      }

      if (request.params) {
        requestConfig.params = request.params;
      }

      // Execute API call
      const response = await firstValueFrom(this.httpService.request(requestConfig));
      
      // Build response
      const apiResponse: ApiCallResponseDto = {
        success: true,
        statusCode: response.status,
        data: response.data,
        headers: response.headers as Record<string, string>,
        executionTime: Date.now() - startTime,
      };

      this.logger.debug(`API call completed: ${response.status} in ${apiResponse.executionTime}ms`);
      return apiResponse;
    } catch (error) {
      this.logger.error(`API call failed: ${error.message}`);
      
      const errorResponse: ApiCallResponseDto = {
        success: false,
        statusCode: error.response?.status || 500,
        data: error.response?.data || { error: error.message },
        headers: error.response?.headers || {},
        executionTime: Date.now() - startTime,
        error: {
          message: error.message,
          code: error.code,
          stack: error.stack,
        },
      };

      return errorResponse;
    }
  }

  // Simplified method for making direct HTTP calls
  async makeHttpCall(
    method: string,
    url: string,
    options: {
      headers?: Record<string, string>;
      data?: any;
      params?: Record<string, any>;
      timeout?: number;
    } = {}
  ): Promise<any> {
    try {
      const requestConfig: AxiosRequestConfig = {
        method: method as any,
        url,
        headers: options.headers || {},
        timeout: options.timeout || this.defaultTimeout,
      };

      if (options.data) {
        requestConfig.data = options.data;
      }

      if (options.params) {
        requestConfig.params = options.params;
      }

      const response = await firstValueFrom(this.httpService.request(requestConfig));
      return response.data;
    } catch (error) {
      this.logger.error(`HTTP call failed: ${error.message}`);
      throw error;
    }
  }
}
