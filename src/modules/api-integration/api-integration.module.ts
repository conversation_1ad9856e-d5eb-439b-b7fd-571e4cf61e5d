import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

import { ApiCallerService } from './services/api-caller.service';
import { SchemaParserService } from './services/schema-parser.service';
import { ApiIntegrationController } from './controllers/api-integration.controller';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  controllers: [ApiIntegrationController],
  providers: [
    ApiCallerService,
    SchemaParserService,
  ],
  exports: [
    ApiCallerService,
    SchemaParserService,
  ],
})
export class ApiIntegrationModule {}
