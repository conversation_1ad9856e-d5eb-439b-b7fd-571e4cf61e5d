import { 
  Controller, 
  Post, 
  Get, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards, 
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request } from 'express';

import { ApiCallerService } from '../services/api-caller.service';
import { SchemaParserService } from '../services/schema-parser.service';
import { 
  CreateApiEndpointDto,
  ApiCallRequestDto,
  ApiCallResponseDto,
  ParseApiSchemaDto,
  ApiSchemaParseResultDto,
  ApiEndpointTestDto,
  ApiEndpointTestResultDto,
} from '../dto/api-integration.dto';
import { ApiEndpoint } from '../../../entities';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@ApiTags('API Integration')
@Controller('api-integration')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ApiIntegrationController {
  constructor(
    private readonly apiCallerService: ApiCallerService,
    private readonly schemaParserService: SchemaParserService,
  ) {}

  @Post('call')
  @ApiOperation({ summary: 'Execute an API call' })
  @ApiResponse({ 
    status: 200, 
    description: 'API call executed successfully',
    type: ApiCallResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid request parameters' })
  @ApiResponse({ status: 404, description: 'API endpoint not found' })
  async callApi(
    @Body() callRequest: ApiCallRequestDto,
  ): Promise<ApiCallResponseDto> {
    return await this.apiCallerService.callApi(callRequest);
  }

  @Post('parse-schema')
  @ApiOperation({ summary: 'Parse API schema (OpenAPI/Swagger/Postman)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Schema parsed successfully',
    type: ApiSchemaParseResultDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid schema format' })
  async parseSchema(
    @Req() req: Request,
    @Body() parseDto: ParseApiSchemaDto,
  ): Promise<ApiSchemaParseResultDto> {
    const userId = (req.user as any)?.id || 'anonymous';
    return await this.schemaParserService.parseApiSchema(userId, parseDto);
  }

  @Post('import-endpoints')
  @ApiOperation({ summary: 'Import endpoints from parsed schema' })
  @ApiResponse({ 
    status: 201, 
    description: 'Endpoints imported successfully',
  })
  async importEndpoints(
    @Req() req: Request,
    @Body() parseResult: ApiSchemaParseResultDto,
  ): Promise<{ 
    imported: number; 
    endpoints: ApiEndpoint[];
  }> {
    const userId = (req.user as any)?.id || 'anonymous';
    const endpoints = await this.schemaParserService.importEndpoints(userId, parseResult);
    
    return {
      imported: endpoints.length,
      endpoints,
    };
  }

  @Post('test/:endpointId')
  @ApiOperation({ summary: 'Test an API endpoint' })
  @ApiResponse({ 
    status: 200, 
    description: 'Endpoint test completed',
    type: ApiEndpointTestResultDto,
  })
  async testEndpoint(
    @Param('endpointId') endpointId: string,
    @Body() testDto: Omit<ApiEndpointTestDto, 'endpointId'>,
  ): Promise<ApiEndpointTestResultDto> {
    const startTime = Date.now();
    
    try {
      const callRequest: ApiCallRequestDto = {
        endpointId,
        parameters: testDto.testParameters,
        enableCaching: false, // Don't cache test requests
      };

      const response = await this.apiCallerService.callApi(callRequest);
      
      const testResult: ApiEndpointTestResultDto = {
        success: response.success,
        statusCode: response.statusCode,
        responseTime: Date.now() - startTime,
        response: response.data,
        validation: {
          valid: true, // TODO: Implement schema validation
          errors: [],
        },
        metadata: {
          timestamp: new Date().toISOString(),
          endpoint: response.endpoint.path,
          method: response.endpoint.method,
        },
      };

      // Validate expected status if provided
      if (testDto.expectedStatus && response.statusCode !== testDto.expectedStatus) {
        testResult.validation.valid = false;
        testResult.validation.errors.push(
          `Expected status ${testDto.expectedStatus}, got ${response.statusCode}`
        );
      }

      return testResult;

    } catch (error) {
      return {
        success: false,
        statusCode: 500,
        responseTime: Date.now() - startTime,
        response: null,
        error: error.message,
        validation: {
          valid: false,
          errors: [error.message],
        },
        metadata: {
          timestamp: new Date().toISOString(),
          endpoint: endpointId,
          method: 'UNKNOWN',
        },
      };
    }
  }

  @Delete('cache')
  @ApiOperation({ summary: 'Clear API response cache' })
  @ApiResponse({ 
    status: 200, 
    description: 'Cache cleared successfully',
  })
  async clearCache(
    @Query('endpointId') endpointId?: string,
  ): Promise<{ message: string }> {
    await this.apiCallerService.clearCache(endpointId);
    
    return {
      message: endpointId 
        ? `Cache cleared for endpoint ${endpointId}` 
        : 'All API cache cleared',
    };
  }

  @Get('endpoints')
  @ApiOperation({ summary: 'Get all API endpoints' })
  @ApiResponse({ 
    status: 200, 
    description: 'List of API endpoints',
  })
  async getEndpoints(
    @Query('status') status?: string,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<{
    endpoints: any[];
    total: number;
  }> {
    // Implementation would go here
    return {
      endpoints: [],
      total: 0,
    };
  }

  @Get('endpoints/:id')
  @ApiOperation({ summary: 'Get API endpoint details' })
  @ApiResponse({ 
    status: 200, 
    description: 'API endpoint details',
  })
  @ApiResponse({ status: 404, description: 'Endpoint not found' })
  async getEndpoint(
    @Param('id') id: string,
  ): Promise<ApiEndpoint> {
    // Implementation would go here
    return {} as ApiEndpoint;
  }

  @Put('endpoints/:id')
  @ApiOperation({ summary: 'Update API endpoint' })
  @ApiResponse({ 
    status: 200, 
    description: 'Endpoint updated successfully',
  })
  async updateEndpoint(
    @Param('id') id: string,
    @Body() updateDto: Partial<CreateApiEndpointDto>,
  ): Promise<ApiEndpoint> {
    // Implementation would go here
    return {} as ApiEndpoint;
  }

  @Delete('endpoints/:id')
  @ApiOperation({ summary: 'Delete API endpoint' })
  @ApiResponse({ 
    status: 200, 
    description: 'Endpoint deleted successfully',
  })
  async deleteEndpoint(
    @Param('id') id: string,
  ): Promise<{ message: string }> {
    // Implementation would go here
    return { message: 'Endpoint deleted successfully' };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get API integration statistics' })
  @ApiResponse({ 
    status: 200, 
    description: 'API integration statistics',
  })
  async getStats(): Promise<{
    totalEndpoints: number;
    totalCalls: number;
    successRate: number;
    averageResponseTime: number;
    endpointsByStatus: Record<string, number>;
    callsByMethod: Record<string, number>;
  }> {
    // Implementation would go here
    return {
      totalEndpoints: 0,
      totalCalls: 0,
      successRate: 0,
      averageResponseTime: 0,
      endpointsByStatus: {},
      callsByMethod: {},
    };
  }
}
