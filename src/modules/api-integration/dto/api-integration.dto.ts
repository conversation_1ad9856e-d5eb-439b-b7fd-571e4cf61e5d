import { IsString, IsOptional, IsEnum, IsObject, IsArray, IsBoolean, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { HttpMethod, EndpointStatus } from '../../../entities';

export class CreateApiEndpointDto {
  @ApiProperty({ description: 'Endpoint name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Endpoint description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'HTTP method', enum: HttpMethod })
  @IsEnum(HttpMethod)
  method: HttpMethod;

  @ApiProperty({ description: 'API endpoint path' })
  @IsString()
  path: string;

  @ApiProperty({ description: 'Base URL for the API' })
  @IsString()
  baseUrl: string;

  @ApiPropertyOptional({ description: 'Endpoint status', enum: EndpointStatus })
  @IsOptional()
  @IsEnum(EndpointStatus)
  status?: EndpointStatus;

  @ApiPropertyOptional({ description: 'Request schema definition' })
  @IsOptional()
  @IsObject()
  requestSchema?: {
    headers?: Record<string, any>;
    queryParams?: Record<string, any>;
    pathParams?: Record<string, any>;
    body?: Record<string, any>;
  };

  @ApiPropertyOptional({ description: 'Response schema definition' })
  @IsOptional()
  @IsObject()
  responseSchema?: {
    success?: Record<string, any>;
    error?: Record<string, any>;
    examples?: Array<{
      status: number;
      description: string;
      body: any;
    }>;
  };

  @ApiPropertyOptional({ description: 'Authentication configuration' })
  @IsOptional()
  @IsObject()
  authentication?: {
    type: 'bearer' | 'basic' | 'apikey' | 'oauth2' | 'none';
    required: boolean;
    description?: string;
  };

  @ApiPropertyOptional({ description: 'Rate limiting configuration' })
  @IsOptional()
  @IsObject()
  rateLimits?: {
    requestsPerMinute?: number;
    requestsPerHour?: number;
    requestsPerDay?: number;
  };

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: {
    tags?: string[];
    category?: string;
    version?: string;
    deprecated?: boolean;
    deprecationDate?: string;
    replacementEndpoint?: string;
  };
}

export class ApiCallRequestDto {
  @ApiProperty({ description: 'HTTP method' })
  @IsString()
  method: string;

  @ApiProperty({ description: 'Request URL' })
  @IsString()
  url: string;

  @ApiPropertyOptional({ description: 'Request headers' })
  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @ApiPropertyOptional({ description: 'Request body data' })
  @IsOptional()
  data?: any;

  @ApiPropertyOptional({ description: 'Query parameters' })
  @IsOptional()
  @IsObject()
  params?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Request timeout in milliseconds' })
  @IsOptional()
  @IsNumber()
  timeout?: number;
}

// Legacy DTO for endpoint-based calls (for backward compatibility)
export class EndpointApiCallRequestDto {
  @ApiProperty({ description: 'API endpoint ID' })
  @IsString()
  endpointId: string;

  @ApiPropertyOptional({ description: 'Request parameters' })
  @IsOptional()
  @IsObject()
  parameters?: {
    headers?: Record<string, string>;
    queryParams?: Record<string, any>;
    pathParams?: Record<string, any>;
    body?: any;
  };

  @ApiPropertyOptional({ description: 'Override authentication' })
  @IsOptional()
  @IsObject()
  authentication?: {
    type: string;
    credentials: Record<string, string>;
  };

  @ApiPropertyOptional({ description: 'Request timeout in milliseconds' })
  @IsOptional()
  @IsNumber()
  timeout?: number;

  @ApiPropertyOptional({ description: 'Enable response caching' })
  @IsOptional()
  @IsBoolean()
  enableCaching?: boolean;

  @ApiPropertyOptional({ description: 'Cache TTL in seconds' })
  @IsOptional()
  @IsNumber()
  cacheTtl?: number;
}

export class ApiCallResponseDto {
  @ApiProperty({ description: 'Request success status' })
  success: boolean;

  @ApiProperty({ description: 'HTTP status code' })
  statusCode: number;

  @ApiProperty({ description: 'Response data' })
  data: any;

  @ApiPropertyOptional({ description: 'Error details if request failed' })
  error?: {
    message: string;
    code?: string;
    stack?: string;
  } | string;

  @ApiProperty({ description: 'Response headers' })
  headers: Record<string, string>;

  @ApiProperty({ description: 'Request execution time in milliseconds' })
  executionTime: number;

  @ApiPropertyOptional({ description: 'Endpoint information' })
  endpoint?: {
    id: string;
    name: string;
    method: string;
    path: string;
    baseUrl: string;
  };

  @ApiPropertyOptional({ description: 'Cache information' })
  cache?: {
    hit: boolean;
    ttl: number;
  };
}

export class ParseApiSchemaDto {
  @ApiProperty({ description: 'API schema content (OpenAPI/Swagger)' })
  @IsString()
  schema: string;

  @ApiProperty({ description: 'Schema format', enum: ['openapi', 'swagger', 'postman'] })
  @IsEnum(['openapi', 'swagger', 'postman'])
  format: 'openapi' | 'swagger' | 'postman';

  @ApiPropertyOptional({ description: 'Base URL override' })
  @IsOptional()
  @IsString()
  baseUrl?: string;

  @ApiPropertyOptional({ description: 'Import options' })
  @IsOptional()
  @IsObject()
  options?: {
    includeDeprecated?: boolean;
    filterTags?: string[];
    filterPaths?: string[];
  };
}

export class ApiSchemaParseResultDto {
  @ApiProperty({ description: 'Number of endpoints parsed' })
  endpointsCount: number;

  @ApiProperty({ description: 'Parsed endpoints' })
  endpoints: Array<{
    name: string;
    method: HttpMethod;
    path: string;
    description?: string;
    tags?: string[];
    parameters?: any;
    responses?: any;
  }>;

  @ApiProperty({ description: 'Schema metadata' })
  metadata: {
    title?: string;
    version?: string;
    description?: string;
    baseUrl?: string;
  };

  @ApiProperty({ description: 'Parsing warnings' })
  warnings: string[];

  @ApiProperty({ description: 'Parsing errors' })
  errors: string[];
}

export class ApiEndpointTestDto {
  @ApiProperty({ description: 'API endpoint ID' })
  @IsString()
  endpointId: string;

  @ApiPropertyOptional({ description: 'Test parameters' })
  @IsOptional()
  @IsObject()
  testParameters?: {
    headers?: Record<string, string>;
    queryParams?: Record<string, any>;
    pathParams?: Record<string, any>;
    body?: any;
  };

  @ApiPropertyOptional({ description: 'Expected response status' })
  @IsOptional()
  @IsNumber()
  expectedStatus?: number;

  @ApiPropertyOptional({ description: 'Validate response schema' })
  @IsOptional()
  @IsBoolean()
  validateSchema?: boolean;
}

export class ApiEndpointTestResultDto {
  @ApiProperty({ description: 'Test success status' })
  success: boolean;

  @ApiProperty({ description: 'HTTP status code' })
  statusCode: number;

  @ApiProperty({ description: 'Response time in milliseconds' })
  responseTime: number;

  @ApiProperty({ description: 'Response data' })
  response: any;

  @ApiPropertyOptional({ description: 'Error details if test failed' })
  error?: string;

  @ApiProperty({ description: 'Schema validation results' })
  validation: {
    valid: boolean;
    errors: string[];
  };

  @ApiProperty({ description: 'Test metadata' })
  metadata: {
    timestamp: string;
    endpoint: string;
    method: string;
  };
}
