import { ApiProperty } from '@nestjs/swagger';
export class Device {
    @ApiProperty()
    uuid: string;

    @ApiProperty()
    deviceToken: string;

    @ApiProperty()
    cardToken: string;

    @ApiProperty()
    sysAccountId: string;

    @ApiProperty()
    timeStamp: string;

    constructor(uuid: string, deviceToken: string, cardToken: string, sysAccountId: string, timeStamp: string) {
        this.uuid = uuid;
        this.deviceToken = deviceToken;
        this.cardToken = cardToken;
        this.sysAccountId = sysAccountId;
        this.timeStamp = timeStamp;
    }
}