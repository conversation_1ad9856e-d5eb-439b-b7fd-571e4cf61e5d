import { Body, Controller, Post, UseGuards, Inject, InternalServerErrorException, BadRequestException, HttpStatus, UseInterceptors, Get, Query } from '@nestjs/common';

import { DeviceService } from './device.service';
import { JWTAuthGuard } from '../../core/guards/jwt.auth.guard';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { AppLogger } from '../../core/logger/logger.service';
import { isNonEmptyString } from '../../utils/custom-validators';

@Controller({ version: '1' })
@UseGuards(JWTAuthGuard)
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
export class DeviceController {
    constructor(@Inject(DeviceService) private readonly deviceService: DeviceService,
        private readonly appLogger: AppLogger,) { }
}
