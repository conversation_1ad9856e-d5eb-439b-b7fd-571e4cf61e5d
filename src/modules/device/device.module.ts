import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>ce<PERSON>ontroller } from './device.controller';
import { DeviceService } from './device.service';
import { HttpModule } from '@nestjs/axios';
import { AppLogger } from '../../core/logger/logger.service';
import { AuthService } from '../auth/auth.service';
import { MWHttpService } from '../../core/service/http.service';

@Module({
    controllers: [DeviceController],
    providers: [DeviceService, AppLogger, AuthService, MWHttpService],
    imports: [
        HttpModule.registerAsync({
            useFactory: () => ({
                timeout: 10000,
                maxRedirects: 5,
            }),
        })
    ]
})
export class DeviceModule {
    constructor(private deviceService: DeviceService) { }
}
