import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PushController } from './push.controller';
import { PushService } from './push.service';
import { JwtService, JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AppLogger } from '../../core/logger/logger.service';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { DeviceService } from '../device/device.service';
import * as admin from "firebase-admin";
import { PushMessageService } from './push.message.service';
import { AuthService } from '../auth/auth.service';
import { MWHttpService } from '../../core/service/http.service';

@Module({
  controllers: [PushController],
  providers: [PushService, JwtService, ConfigService, AppLogger, LoggingInterceptor,
    DeviceService, PushMessageService, AuthService, MWHttpService],
  imports: [
    HttpModule.registerAsync({
      useFactory: () => ({
        timeout: 10000,
        maxRedirects: 5,
      }),
    }),
  ]
})
export class PushModule {
  constructor() {
    const buffer = Buffer.from(process.env.PRIVATE_KEY, 'base64');
    const privateKey = buffer.toString('utf-8');
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.PROJECT_ID,
        clientEmail: process.env.FIREBASE_SERVICE_ACCOUNT,
        privateKey: privateKey,
      }),
    });
  }
}
