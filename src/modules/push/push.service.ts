import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { JwtService } from '@nestjs/jwt';
import { grantyType, algorithm, acceptEncoding } from '../../utils/const';
import { NotificationMessage } from '../../models/notification.message.dto';
import { PushRequestDto } from '../../models/push.request.dto';

@Injectable()
export class PushService {

    constructor(private readonly configService: ConfigService, private jwtService: JwtService) { }

    //Generate the FCM access token
    async getAccessToken(): Promise<string> {
        try {

            const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds

            const payload = {
                iss: process.env.FIREBASE_SERVICE_ACCOUNT,
                sub: process.env.FIREBASE_SERVICE_ACCOUNT,
                aud: process.env.AUDIENCE,
                scope: process.env.FCM_SCOPE,
                kid: process.env.PRIVATE_KEY_ID,
                iat: currentTime,
                exp: currentTime + 3600,
            };
            const jwtToken = this.jwtService.sign(payload, { algorithm: algorithm, privateKey: process.env.PRIVATE_KEY });

            const apiUrl = process.env.AUDIENCE;

            const body = {
                grant_type: grantyType,
                assertion: jwtToken,
            };

            // API request to obtain access token
            const response = await axios.post(apiUrl, body, {
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (response.status !== 200) {
                console.error('API request failed');
                console.error(response.status, response.data);
                throw new HttpException(
                    'Failed to obtain access token',
                    HttpStatus.INTERNAL_SERVER_ERROR,
                );
            }

            console.log('API request successful');
            const tokenResponse = response.data;
            const accessToken = tokenResponse.access_token;

            console.log(`FCM Access Token: ${accessToken}`);

            return accessToken;

        } catch (error) {
            console.error('Error occurred:', error.message);
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Push the FCM message to mobile platform
    async sendMessage(accessToken: string, message: string): Promise<string> {
        try {

            // URL of the Google API you want to access
            const firebaseApiUrl = process.env.SEND_MESSAGE;

            const requestHeaders = {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Accept-Encoding': acceptEncoding,
            };

            // FCM Notification API Call
            const firebaseResponse = await axios.post(firebaseApiUrl, message, {
                headers: requestHeaders,
            });

            if (firebaseResponse.status === 200) {
                console.log('Firebase Cloud Message Sent Successfully');
                return firebaseResponse.statusText;
            } else {
                console.error('Firebase Send Message failed');
                console.error(firebaseResponse.status, firebaseResponse.data);
                return firebaseResponse.data;
            }
        } catch (error) {
            console.error('Error occurred:', error.message);
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async generateMessageTemplate(request: PushRequestDto): Promise<string> {
        const message: NotificationMessage = new NotificationMessage();
        message.token = request.deviceToken;
        message.notification = { message: request.message, title: request.title };
        message.data = { content: request.data };
        message.android = {
            "priority": "normal",
            "ttl": "4500s"
        };
        message.apns = {
            "headers": {
                "apns-priority": "5",
                "apns-expiration": "86400000"
            }
        };
        return `{"message": ${JSON.stringify(message)}}`;
    }
}
