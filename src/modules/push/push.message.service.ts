import { Injectable } from "@nestjs/common";
import * as admin from "firebase-admin";
import { AppLogger } from '../../core/logger/logger.service';
import {
    IdsDto,
    MultipleDeviceNotificationDto,
    NotificationDto,
    TopicNotificationDto,
} from "../../models/notification.dto";
import { BatchResponse } from "firebase-admin/lib/messaging/messaging-api";
import { success, failed } from '../../utils/const';

@Injectable()
export class PushMessageService {

    constructor(private appLogger: AppLogger) { }

    async sendNotification({ deviceToken, title, message, data }: NotificationDto) {
        this.appLogger.log("SendMessage: Title", title);
        try {
            const response = await admin.messaging().send({
                token: deviceToken,
                android: {
                    priority: "high",
                    ttl: 4500
                },
                apns: {
                    headers: {
                        "apns-priority": "10",
                        "apns-expiration": "86400000"
                    },
                    payload: {
                        aps: {
                            contentAvailable: true,
                            sound: "default",
                            mutableContent: true
                        }
                    }
                },
                notification: {
                    body: message, title: title
                },
                data: data,
            });
            return {
                success: true,
                message: `${response}`,
            };
        } catch (error) {
            this.appLogger.log("SendMessage", JSON.stringify(error));
            return { success: false, message: "Failed to send notification" };
        }
    }

    async sendNotificationToMultipleTokens({
        ids,
        title,
        message,
        data,
    }: MultipleDeviceNotificationDto) {
        const notificationMessage = {
            notification: {
                body: message, title: title,
            },
            android: {
                ttl: 4500
            },
            apns: {
                headers: {
                    "apns-priority": "10",
                    "apns-expiration": "86400000"
                },
                payload: {
                    aps: {
                        contentAvailable: true,
                        sound: "default",
                        mutableContent: true
                    }
                }
            },
            tokens: ids.map(id => id.deviceToken),
            data: data
        };

        try {
            const response = await admin.messaging().sendEachForMulticast(notificationMessage);
            const statusResponse = this.getNotificationStatus(response, ids);
            this.appLogger.log("Card Decline Data:", JSON.stringify(data));
            this.appLogger.log("Successfully sent messages:", JSON.stringify(statusResponse));
            return statusResponse;
        } catch (error) {
            this.appLogger.log("Error sending messages:", JSON.stringify(error));
            return { success: false, message: "Failed to send notifications" };
        }
    }

    async sendTopicNotification({
        topic,
        title,
        message,
    }: TopicNotificationDto) {
        const notificationMessage = {
            notification: {
                title,
                message,
            },
            topic,
        };

        try {
            const response = await admin.messaging().send(notificationMessage);
            this.appLogger.log("Successfully sent message:", JSON.stringify(response));
            return { success: true, message: "Topic notification sent successfully" };
        } catch (error) {
            this.appLogger.log("Error sending message:", JSON.stringify(error));
            return { success: false, message: "Failed to send topic notification" };
        }
    }

    getNotificationStatus(response: BatchResponse, ids: IdsDto[]): any {

        return response.responses.map((response, index) => {
            return {
                fuel_session_id: ids[index].fuel_session_id,
                status: response.success ? success : failed,
                error: response.error || null,
            };
        });
    }
}