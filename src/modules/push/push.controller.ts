import { Body, Controller, Post, UseInterceptors, UsePipes, ValidationPipe, Inject, UseGuards } from '@nestjs/common';
import { TimeoutInterceptor } from '../../core/interceptors/timeout.interceptor';
import { LoggingInterceptor } from '../../core/interceptors/logging.interceptor';
import { PushRequestDto } from '../../models/push.request.dto';
import { PushService } from './push.service';
import { PushMessageService } from './push.message.service';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JWTAuthGuard } from '../../core/guards/jwt.auth.guard';
import { MultipleDeviceNotificationDto, NotificationDto } from '../../models/notification.dto';

@Controller({ version: '1' })
@UseInterceptors(TimeoutInterceptor, LoggingInterceptor)
@UsePipes(new ValidationPipe({ transform: true }))
@UseGuards(JWTAuthGuard)
export class PushController {

    constructor(private pushService: PushService, private readonly pushMessageService: PushMessageService,) { }

    @Post('push/message')
    @ApiOperation({ summary: "Send a push notification to a single device" })
    @ApiResponse({ status: 200, description: "Notification sent successfully" })
    async pushMessage(@Body() request: MultipleDeviceNotificationDto) {
        return await this.pushMessageService.sendNotificationToMultipleTokens({
            ids: request.ids,
            message: request.message,
            title: request.title,
            data: request.data,
        });
    }

    @Post('push/messageV2')
    @ApiOperation({ summary: "Send a push notification to a single device" })
    @ApiResponse({ status: 200, description: "Notification sent successfully" })
    async pushMessageV2(@Body() request: NotificationDto) {
        return await this.pushMessageService.sendNotification({
            deviceToken: request.deviceToken,
            message: request.message,
            title: request.title,
            data: request.data,
        });
    }

    @Post('push/messageByToken')
    @ApiOperation({ summary: "Send a push notification to a single device" })
    @ApiResponse({ status: 200, description: "Notification sent successfully" })
    async pushMessageUsingToken(@Body() request: PushRequestDto){
        const fcmToken = await this.pushService.getAccessToken();
        const messageTemplate = await this.pushService.generateMessageTemplate(request);
        console.log(messageTemplate);
        const response = await this.pushService.sendMessage(fcmToken, messageTemplate);
    }
}
