import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI, GenerativeModel, ChatSession } from '@google/generative-ai';
import {
  IAIProvider,
  AIRequestOptions,
  AIResponse,
  AIStreamChunk,
  AIMessage,
} from '../interfaces/ai-provider.interface';

@Injectable()
export class GeminiProvider implements IAIProvider {
  private readonly logger = new Logger(GeminiProvider.name);
  private readonly genAI: GoogleGenerativeAI;

  // Pricing per 1K tokens (as of 2024)
  private readonly pricing = {
    'gemini-1.5-pro': { prompt: 0.0035, completion: 0.0105 },
    'gemini-1.5-flash': { prompt: 0.00035, completion: 0.00105 },
    'gemini-pro': { prompt: 0.0005, completion: 0.0015 },
  };

  constructor(private configService: ConfigService) {
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
  }

  async generateResponse(options: AIRequestOptions): Promise<AIResponse> {
    try {
      const startTime = Date.now();
      
      const model = this.genAI.getGenerativeModel({ model: options.model });
      
      // Convert messages to Gemini format
      const { prompt, history } = this.convertMessagesToGeminiFormat(options.messages);
      
      // Start chat session with history
      const chat = model.startChat({
        history,
        generationConfig: {
          temperature: options.temperature || 0.7,
          maxOutputTokens: options.maxTokens || 1000,
        },
      });

      const result = await chat.sendMessage(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Calculate token usage (approximate)
      const promptTokens = this.estimateTokens(prompt);
      const completionTokens = this.estimateTokens(text);
      const totalTokens = promptTokens + completionTokens;
      
      const usage = {
        promptTokens,
        completionTokens,
        totalTokens,
      };

      const cost = this.calculateCost(options.model, promptTokens, completionTokens);
      const responseTime = Date.now() - startTime;

      this.logger.debug(`Gemini response generated in ${responseTime}ms`);

      return {
        message: {
          role: 'assistant',
          content: text,
        },
        usage,
        model: options.model,
        finishReason: response.candidates?.[0]?.finishReason || 'stop',
        cost,
      };

    } catch (error) {
      this.logger.error('Gemini API error:', error);
      throw error;
    }
  }

  async *generateStreamResponse(options: AIRequestOptions): AsyncIterable<AIStreamChunk> {
    try {
      const model = this.genAI.getGenerativeModel({ model: options.model });
      
      // Convert messages to Gemini format
      const { prompt, history } = this.convertMessagesToGeminiFormat(options.messages);
      
      // Start chat session with history
      const chat = model.startChat({
        history,
        generationConfig: {
          temperature: options.temperature || 0.7,
          maxOutputTokens: options.maxTokens || 1000,
        },
      });

      const result = await chat.sendMessageStream(prompt);
      
      let fullText = '';
      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        fullText += chunkText;
        
        yield {
          delta: {
            role: 'assistant',
            content: chunkText,
          },
        };
      }

      // Final chunk with usage information
      const promptTokens = this.estimateTokens(prompt);
      const completionTokens = this.estimateTokens(fullText);
      
      yield {
        delta: {},
        finishReason: 'stop',
        usage: {
          promptTokens,
          completionTokens,
          totalTokens: promptTokens + completionTokens,
        },
      };

    } catch (error) {
      this.logger.error('Gemini streaming error:', error);
      yield {
        delta: {},
        finishReason: 'error',
      };
    }
  }

  calculateCost(model: string, promptTokens: number, completionTokens: number): number {
    const modelPricing = this.pricing[model] || this.pricing['gemini-pro'];
    const promptCost = (promptTokens / 1000) * modelPricing.prompt;
    const completionCost = (completionTokens / 1000) * modelPricing.completion;
    return promptCost + completionCost;
  }

  getAvailableModels(): string[] {
    return [
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-pro',
    ];
  }

  private convertMessagesToGeminiFormat(messages: AIMessage[]): { prompt: string; history: any[] } {
    const history = [];
    let prompt = '';
    
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      
      if (message.role === 'system') {
        // System messages are handled as part of the model configuration
        continue;
      }
      
      if (i === messages.length - 1 && message.role === 'user') {
        // Last user message becomes the prompt
        prompt = message.content;
      } else {
        // Convert to Gemini history format
        history.push({
          role: message.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: message.content }],
        });
      }
    }
    
    return { prompt, history };
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }
}
