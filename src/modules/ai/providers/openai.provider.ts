import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import {
  IAIProvider,
  AIRequestOptions,
  AIResponse,
  AIStreamChunk,
  AIMessage,
} from '../interfaces/ai-provider.interface';

@Injectable()
export class OpenAIProvider implements IAIProvider {
  private readonly logger = new Logger(OpenAIProvider.name);
  private readonly openai: OpenAI;

  // Pricing per 1K tokens (as of 2024)
  private readonly pricing = {
    'gpt-4': { prompt: 0.03, completion: 0.06 },
    'gpt-4-turbo': { prompt: 0.01, completion: 0.03 },
    'gpt-3.5-turbo': { prompt: 0.0015, completion: 0.002 },
  };

  constructor(private configService: ConfigService) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }

  async generateResponse(options: AIRequestOptions): Promise<AIResponse> {
    try {
      const startTime = Date.now();
      
      const response = await this.openai.chat.completions.create({
        model: options.model,
        messages: options.messages as any,
        temperature: options.temperature,
        max_tokens: options.maxTokens,
        functions: options.functions as any,
        function_call: options.functionCall as any,
      });

      const choice = response.choices[0];
      const usage = response.usage;
      const responseTime = Date.now() - startTime;

      const aiResponse: AIResponse = {
        message: {
          role: choice.message.role as any,
          content: choice.message.content || '',
          functionCall: choice.message.function_call ? {
            name: choice.message.function_call.name,
            arguments: choice.message.function_call.arguments,
          } : undefined,
          toolCalls: choice.message.tool_calls?.map(call => ({
            id: call.id,
            type: call.type,
            function: {
              name: call.function.name,
              arguments: call.function.arguments,
            },
          })),
        },
        usage: {
          promptTokens: usage?.prompt_tokens || 0,
          completionTokens: usage?.completion_tokens || 0,
          totalTokens: usage?.total_tokens || 0,
        },
        model: response.model,
        finishReason: choice.finish_reason || 'stop',
        cost: this.calculateCost(
          options.model,
          usage?.prompt_tokens || 0,
          usage?.completion_tokens || 0,
        ),
      };

      this.logger.log(`OpenAI response generated in ${responseTime}ms`);
      return aiResponse;
    } catch (error) {
      this.logger.error('OpenAI API error:', error);
      throw error;
    }
  }

  async* generateStreamResponse(options: AIRequestOptions): AsyncIterable<AIStreamChunk> {
    try {
      const stream = await this.openai.chat.completions.create({
        model: options.model,
        messages: options.messages as any,
        temperature: options.temperature,
        max_tokens: options.maxTokens,
        functions: options.functions as any,
        function_call: options.functionCall as any,
        stream: true,
      });

      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        if (choice) {
          yield {
            delta: {
              role: choice.delta.role as any,
              content: choice.delta.content || '',
              functionCall: choice.delta.function_call ? {
                name: choice.delta.function_call.name,
                arguments: choice.delta.function_call.arguments,
              } : undefined,
            },
            finishReason: choice.finish_reason || undefined,
            usage: chunk.usage ? {
              promptTokens: chunk.usage.prompt_tokens,
              completionTokens: chunk.usage.completion_tokens,
              totalTokens: chunk.usage.total_tokens,
            } : undefined,
          };
        }
      }
    } catch (error) {
      this.logger.error('OpenAI streaming error:', error);
      throw error;
    }
  }

  calculateCost(model: string, promptTokens: number, completionTokens: number): number {
    const modelPricing = this.pricing[model] || this.pricing['gpt-3.5-turbo'];
    const promptCost = (promptTokens / 1000) * modelPricing.prompt;
    const completionCost = (completionTokens / 1000) * modelPricing.completion;
    return promptCost + completionCost;
  }

  getAvailableModels(): string[] {
    return Object.keys(this.pricing);
  }
}
