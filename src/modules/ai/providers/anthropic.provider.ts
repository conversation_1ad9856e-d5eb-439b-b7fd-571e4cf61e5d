import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Anthropic from '@anthropic-ai/sdk';
import {
  IAIProvider,
  AIRequestOptions,
  AIResponse,
  AIStreamChunk,
  AIMessage,
} from '../interfaces/ai-provider.interface';

@Injectable()
export class AnthropicProvider implements IAIProvider {
  private readonly logger = new Logger(AnthropicProvider.name);
  private readonly anthropic: Anthropic;

  // Pricing per 1K tokens (as of 2024)
  private readonly pricing = {
    'claude-3-opus-20240229': { prompt: 0.015, completion: 0.075 },
    'claude-3-sonnet-20240229': { prompt: 0.003, completion: 0.015 },
    'claude-3-haiku-20240307': { prompt: 0.00025, completion: 0.00125 },
  };

  constructor(private configService: ConfigService) {
    this.anthropic = new Anthropic({
      apiKey: this.configService.get<string>('ANTHROPIC_API_KEY'),
    });
  }

  async generateResponse(options: AIRequestOptions): Promise<AIResponse> {
    try {
      const startTime = Date.now();
      
      // Convert messages to Anthropic format
      const messages = this.convertMessages(options.messages);
      const systemMessage = options.messages.find(m => m.role === 'system')?.content;

      const response = await this.anthropic.messages.create({
        model: options.model,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature,
        system: systemMessage,
        messages: messages as any,
      });

      const responseTime = Date.now() - startTime;
      const content = response.content[0];
      const usage = response.usage;

      const aiResponse: AIResponse = {
        message: {
          role: 'assistant',
          content: content.type === 'text' ? content.text : '',
        },
        usage: {
          promptTokens: usage.input_tokens,
          completionTokens: usage.output_tokens,
          totalTokens: usage.input_tokens + usage.output_tokens,
        },
        model: response.model,
        finishReason: response.stop_reason || 'stop',
        cost: this.calculateCost(
          options.model,
          usage.input_tokens,
          usage.output_tokens,
        ),
      };

      this.logger.log(`Anthropic response generated in ${responseTime}ms`);
      return aiResponse;
    } catch (error) {
      this.logger.error('Anthropic API error:', error);
      throw error;
    }
  }

  async* generateStreamResponse(options: AIRequestOptions): AsyncIterable<AIStreamChunk> {
    try {
      const messages = this.convertMessages(options.messages);
      const systemMessage = options.messages.find(m => m.role === 'system')?.content;

      const stream = await this.anthropic.messages.create({
        model: options.model,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature,
        system: systemMessage,
        messages: messages as any,
        stream: true,
      });

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          yield {
            delta: {
              content: chunk.delta.text,
            },
          };
        } else if (chunk.type === 'message_stop') {
          yield {
            delta: { content: '' },
            finishReason: 'stop',
          };
        }
      }
    } catch (error) {
      this.logger.error('Anthropic streaming error:', error);
      throw error;
    }
  }

  private convertMessages(messages: AIMessage[]): Array<{ role: 'user' | 'assistant'; content: string }> {
    return messages
      .filter(m => m.role !== 'system')
      .map(m => ({
        role: m.role === 'user' ? 'user' : 'assistant',
        content: m.content,
      }));
  }

  calculateCost(model: string, promptTokens: number, completionTokens: number): number {
    const modelPricing = this.pricing[model] || this.pricing['claude-3-haiku-20240307'];
    const promptCost = (promptTokens / 1000) * modelPricing.prompt;
    const completionCost = (completionTokens / 1000) * modelPricing.completion;
    return promptCost + completionCost;
  }

  getAvailableModels(): string[] {
    return Object.keys(this.pricing);
  }
}
