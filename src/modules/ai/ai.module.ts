import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { AIService } from './services/ai.service';
import { ConversationMemoryService } from './services/conversation-memory.service';
import { FunctionCallingService } from './services/function-calling.service';
import { OpenAIProvider } from './providers/openai.provider';
import { AnthropicProvider } from './providers/anthropic.provider';
import { AIController } from './controllers/ai.controller';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
  ],
  controllers: [AIController],
  providers: [
    AIService,
    ConversationMemoryService,
    FunctionCallingService,
    OpenAIProvider,
    AnthropicProvider,
  ],
  exports: [
    AIService,
    ConversationMemoryService,
    FunctionCallingService,
  ],
})
export class AIModule {}
