import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { User, Conversation, Message, KnowledgeBase, ApiEndpoint } from '../../entities';
import { AIService } from './services/ai.service';
import { ConversationMemoryService } from './services/conversation-memory.service';
import { FunctionCallingService } from './services/function-calling.service';
import { OpenAIProvider } from './providers/openai.provider';
import { AnthropicProvider } from './providers/anthropic.provider';
import { AIController } from './controllers/ai.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Conversation,
      Message,
      KnowledgeBase,
      ApiEndpoint,
    ]),
    ConfigModule,
    EventEmitterModule,
  ],
  controllers: [AIController],
  providers: [
    AIService,
    ConversationMemoryService,
    FunctionCallingService,
    OpenAIProvider,
    AnthropicProvider,
  ],
  exports: [
    AIService,
    ConversationMemoryService,
    FunctionCallingService,
  ],
})
export class AIModule {}
