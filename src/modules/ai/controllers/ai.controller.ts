import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Query,
  UseGuards,
  Req,
  Res,
  Sse,
  MessageEvent,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';

import { AIService } from '../services/ai.service';
import {
  ChatRequestDto,
  ChatResponseDto,
  StreamChatDto,
  ConversationListDto,
  ConversationDetailDto,
  ModelsListDto,
  UsageStatsDto,
  StreamChunkDto,
  ClearConversationDto
} from '../dto/chat.dto';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';


@ApiTags('AI')
@Controller('ai')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AIController {
  constructor(private readonly aiService: AIService) {}

  @Post('chat')
  @ApiOperation({
    summary: 'Send a chat message to AI',
    description: 'Send a message to the AI and receive a complete response. Supports various AI models, conversation context, and function calling.'
  })
  @ApiResponse({
    status: 200,
    description: 'AI response generated successfully',
    type: ChatResponseDto,
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Hello! How can I help you today?' },
        conversationId: { type: 'string', example: 'conv_123456789' },
        messageId: { type: 'string', example: 'msg_987654321' },
        model: { type: 'string', example: 'gpt-4' },
        usage: {
          type: 'object',
          properties: {
            promptTokens: { type: 'number', example: 15 },
            completionTokens: { type: 'number', example: 8 },
            totalTokens: { type: 'number', example: 23 }
          }
        },
        cost: { type: 'number', example: 0.00069 },
        responseTime: { type: 'number', example: 1250 }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request - malformed input or validation errors',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'array', items: { type: 'string' }, example: ['message must be a string'] },
        error: { type: 'string', example: 'Bad Request' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid or missing authentication token' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded - too many requests' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error - AI service unavailable',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 500 },
        message: { type: 'string', example: 'AI service temporarily unavailable' },
        error: { type: 'string', example: 'Internal Server Error' }
      }
    }
  })
  async chat(
    @Req() req: Request,
    @Body() chatRequest: ChatRequestDto,
  ): Promise<ChatResponseDto> {
    const userId = (req.user as any)?.id || 'anonymous';
    return await this.aiService.chat(userId, chatRequest);
  }

  @Post('stream-chat')
  @ApiOperation({
    summary: 'Send a chat message with streaming response',
    description: 'Send a message to AI and receive a streaming response using Server-Sent Events. Perfect for real-time chat interfaces where you want to show the AI response as it\'s being generated.'
  })
  @ApiResponse({
    status: 200,
    description: 'Streaming AI response in Server-Sent Events format',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      'Connection': { description: 'keep-alive' },
      'Access-Control-Allow-Origin': { description: '*' },
    },
    schema: {
      type: 'string',
      description: 'Server-Sent Events stream with data chunks',
      example: `data: {"type":"start","metadata":{"model":"gpt-4","conversationId":"conv_123"}}

data: {"type":"chunk","delta":"Hello"}

data: {"type":"chunk","delta":" there!"}

data: {"type":"done","metadata":{"tokens":15,"cost":0.0005}}

data: [DONE]`
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid request parameters' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  @ApiResponse({ status: 500, description: 'AI service error during streaming' })
  async streamChat(
    @Req() req: Request,
    @Res() res: Response,
    @Body() streamChatRequest: StreamChatDto,
  ): Promise<void> {
    const userId = (req.user as any)?.id || 'anonymous';

    // Set SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    try {
      const stream = this.aiService.streamChat(userId, streamChatRequest);
      
      for await (const chunk of stream) {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      }
      
      res.write('data: [DONE]\n\n');
    } catch (error) {
      res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
    } finally {
      res.end();
    }
  }

  @Sse('stream-chat-sse')
  @ApiOperation({
    summary: 'Stream chat using Server-Sent Events (GET method)',
    description: 'Alternative streaming endpoint using GET method with query parameters. Useful for simple integrations that prefer GET requests for streaming.'
  })
  @ApiResponse({
    status: 200,
    description: 'Server-Sent Events stream',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
          description: 'Stream chunk data',
          properties: {
            type: { type: 'string', enum: ['start', 'chunk', 'function_call', 'error', 'done'] },
            content: { type: 'string', description: 'Message content' },
            delta: { type: 'string', description: 'Incremental content' },
            metadata: { type: 'object', description: 'Additional metadata' }
          }
        },
        type: { type: 'string', description: 'Event type' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Invalid query parameters' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Streaming error' })
  streamChatSSE(
    @Req() req: Request,
    @Query() query: StreamChatDto,
  ): Observable<MessageEvent> {
    const userId = (req.user as any)?.id || 'anonymous';
    
    return new Observable(observer => {
      (async () => {
        try {
          const stream = this.aiService.streamChat(userId, query);
          
          for await (const chunk of stream) {
            observer.next({
              data: chunk,
              type: 'chunk',
            } as MessageEvent);
          }
          
          observer.next({
            data: { type: 'done' },
            type: 'done',
          } as MessageEvent);
          
          observer.complete();
        } catch (error) {
          observer.error(error);
        }
      })();
    });
  }

  @Get('conversations')
  @ApiOperation({
    summary: 'Get user conversations',
    description: 'Retrieve a paginated list of conversations for the authenticated user. Includes conversation metadata like message count, token usage, and costs.'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Maximum number of conversations to return (default: 20, max: 100)',
    example: 20
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of conversations to skip for pagination (default: 0)',
    example: 0
  })
  @ApiResponse({
    status: 200,
    description: 'List of user conversations with pagination info',
    type: ConversationListDto,
    schema: {
      type: 'object',
      properties: {
        conversations: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'conv_123456789' },
              title: { type: 'string', example: 'Discussion about AI' },
              userId: { type: 'string', example: 'user_987654321' },
              createdAt: { type: 'string', format: 'date-time' },
              updatedAt: { type: 'string', format: 'date-time' },
              messageCount: { type: 'number', example: 15 },
              totalTokens: { type: 'number', example: 1250 },
              totalCost: { type: 'number', example: 0.025 }
            }
          }
        },
        total: { type: 'number', example: 42 },
        limit: { type: 'number', example: 20 },
        offset: { type: 'number', example: 0 }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getConversations(
    @Req() req: Request,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<ConversationListDto> {
    const userId = (req.user as any)?.id || 'anonymous';
    // Implementation would go here - using userId, limit, offset
    return {
      conversations: [],
      total: 0,
      limit: limit || 20,
      offset: offset || 0
    };
  }

  @Get('conversations/:id')
  @ApiOperation({
    summary: 'Get conversation details',
    description: 'Retrieve detailed information about a specific conversation, including all messages, metadata, and usage statistics.'
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Unique conversation identifier',
    example: 'conv_123456789'
  })
  @ApiResponse({
    status: 200,
    description: 'Detailed conversation information including messages',
    type: ConversationDetailDto,
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'conv_123456789' },
        title: { type: 'string', example: 'Discussion about AI' },
        userId: { type: 'string', example: 'user_987654321' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
        messageCount: { type: 'number', example: 15 },
        totalTokens: { type: 'number', example: 1250 },
        totalCost: { type: 'number', example: 0.025 },
        messages: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              role: { type: 'string', enum: ['user', 'assistant', 'system'] },
              content: { type: 'string', example: 'Hello, how can I help you?' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getConversation(
    @Req() req: Request,
    @Param('id') conversationId: string,
  ): Promise<ConversationDetailDto | null> {
    const userId = (req.user as any)?.id || 'anonymous';
    // Implementation would go here - using userId, conversationId
    return null;
  }

  @Post('conversations/:id/clear')
  @ApiOperation({
    summary: 'Clear conversation memory',
    description: 'Clear the conversation memory/context for a specific conversation. This removes all message history but keeps the conversation record.'
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Unique conversation identifier',
    example: 'conv_123456789'
  })
  @ApiResponse({
    status: 200,
    description: 'Conversation memory cleared successfully',
    type: ClearConversationDto,
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Conversation memory cleared successfully' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async clearConversation(
    @Req() req: Request,
    @Param('id') conversationId: string,
  ): Promise<ClearConversationDto> {
    const userId = (req.user as any)?.id || 'anonymous';
    // Implementation would go here - using userId, conversationId
    return {
      success: true,
      message: 'Conversation memory cleared successfully'
    };
  }

  @Get('models')
  @ApiOperation({
    summary: 'Get available AI models',
    description: 'Retrieve a list of all available AI models with their capabilities, pricing, and specifications. Useful for model selection in chat requests.'
  })
  @ApiResponse({
    status: 200,
    description: 'List of available AI models with detailed information',
    type: ModelsListDto,
    schema: {
      type: 'object',
      properties: {
        models: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'gpt-4' },
              name: { type: 'string', example: 'GPT-4' },
              provider: { type: 'string', example: 'OpenAI' },
              description: { type: 'string', example: 'Most capable GPT-4 model' },
              maxTokens: { type: 'number', example: 8192 },
              pricing: {
                type: 'object',
                properties: {
                  prompt: { type: 'number', example: 0.03 },
                  completion: { type: 'number', example: 0.06 }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getAvailableModels(): Promise<ModelsListDto> {
    return {
      models: [
        {
          id: 'gpt-4',
          name: 'GPT-4',
          provider: 'OpenAI',
          description: 'Most capable GPT-4 model',
          maxTokens: 8192,
          pricing: { prompt: 0.03, completion: 0.06 },
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'OpenAI',
          description: 'Fast and efficient model',
          maxTokens: 4096,
          pricing: { prompt: 0.0015, completion: 0.002 },
        },
        {
          id: 'claude-3-sonnet-20240229',
          name: 'Claude 3 Sonnet',
          provider: 'Anthropic',
          description: 'Balanced performance and speed',
          maxTokens: 200000,
          pricing: { prompt: 0.003, completion: 0.015 },
        },
      ],
    };
  }

  @Get('usage')
  @ApiOperation({
    summary: 'Get user AI usage statistics',
    description: 'Retrieve comprehensive usage statistics for the authenticated user, including token consumption, costs, and usage patterns over time.'
  })
  @ApiQuery({
    name: 'period',
    required: false,
    type: String,
    description: 'Time period for usage statistics (7d, 30d, 90d, 1y)',
    example: '30d',
    enum: ['7d', '30d', '90d', '1y']
  })
  @ApiResponse({
    status: 200,
    description: 'Detailed user usage statistics and analytics',
    type: UsageStatsDto,
    schema: {
      type: 'object',
      properties: {
        totalTokens: { type: 'number', example: 15420 },
        totalCost: { type: 'number', example: 2.35 },
        conversationCount: { type: 'number', example: 25 },
        messageCount: { type: 'number', example: 150 },
        modelUsage: {
          type: 'object',
          additionalProperties: { type: 'number' },
          example: { 'gpt-4': 8500, 'gpt-3.5-turbo': 6920 }
        },
        dailyUsage: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string', format: 'date', example: '2024-01-15' },
              tokens: { type: 'number', example: 450 },
              cost: { type: 'number', example: 0.08 },
              messages: { type: 'number', example: 12 }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getUsageStats(
    @Req() req: Request,
    @Query('period') period: string = '30d',
  ): Promise<UsageStatsDto> {
    const userId = (req.user as any)?.id || 'anonymous';
    // Implementation would go here - using userId, period
    return {
      totalTokens: 0,
      totalCost: 0,
      conversationCount: 0,
      messageCount: 0,
      modelUsage: {},
      dailyUsage: [],
    };
  }
}
