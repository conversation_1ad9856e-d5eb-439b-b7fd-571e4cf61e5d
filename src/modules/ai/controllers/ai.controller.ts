import { 
  Controller, 
  Post, 
  Body, 
  Get, 
  Param, 
  Query, 
  UseGuards, 
  Req, 
  Res,
  Sse,
  MessageEvent,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';

import { AIService } from '../services/ai.service';
import { ChatRequestDto, ChatResponseDto, StreamChatDto } from '../dto/chat.dto';
import { JwtAuthGuard } from '../../../core/guards/jwt-auth.guard';

@ApiTags('AI')
@Controller('ai')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AIController {
  constructor(private readonly aiService: AIService) {}

  @Post('chat')
  @ApiOperation({ summary: 'Send a chat message to AI' })
  @ApiResponse({ 
    status: 200, 
    description: 'AI response generated successfully',
    type: ChatResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  async chat(
    @Req() req: Request,
    @Body() chatRequest: ChatRequestDto,
  ): Promise<ChatResponseDto> {
    const userId = req.user?.id;
    return await this.aiService.chat(userId, chatRequest);
  }

  @Post('stream-chat')
  @ApiOperation({ summary: 'Send a chat message with streaming response' })
  @ApiResponse({ 
    status: 200, 
    description: 'Streaming AI response',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      'Connection': { description: 'keep-alive' },
    },
  })
  async streamChat(
    @Req() req: Request,
    @Res() res: Response,
    @Body() streamChatRequest: StreamChatDto,
  ): Promise<void> {
    const userId = req.user?.id;

    // Set SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    try {
      const stream = this.aiService.streamChat(userId, streamChatRequest);
      
      for await (const chunk of stream) {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      }
      
      res.write('data: [DONE]\n\n');
    } catch (error) {
      res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
    } finally {
      res.end();
    }
  }

  @Sse('stream-chat-sse')
  @ApiOperation({ summary: 'Stream chat using Server-Sent Events' })
  streamChatSSE(
    @Req() req: Request,
    @Query() query: StreamChatDto,
  ): Observable<MessageEvent> {
    const userId = req.user?.id;
    
    return new Observable(observer => {
      (async () => {
        try {
          const stream = this.aiService.streamChat(userId, query);
          
          for await (const chunk of stream) {
            observer.next({
              data: chunk,
              type: 'chunk',
            } as MessageEvent);
          }
          
          observer.next({
            data: { type: 'done' },
            type: 'done',
          } as MessageEvent);
          
          observer.complete();
        } catch (error) {
          observer.error(error);
        }
      })();
    });
  }

  @Get('conversations')
  @ApiOperation({ summary: 'Get user conversations' })
  @ApiResponse({ 
    status: 200, 
    description: 'List of user conversations',
  })
  async getConversations(
    @Req() req: Request,
    @Query('limit') limit: number = 20,
    @Query('offset') offset: number = 0,
  ): Promise<any> {
    const userId = req.user?.id;
    // Implementation would go here
    return { conversations: [], total: 0 };
  }

  @Get('conversations/:id')
  @ApiOperation({ summary: 'Get conversation details' })
  @ApiResponse({ 
    status: 200, 
    description: 'Conversation details',
  })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async getConversation(
    @Req() req: Request,
    @Param('id') conversationId: string,
  ): Promise<any> {
    const userId = req.user?.id;
    // Implementation would go here
    return { conversation: null };
  }

  @Post('conversations/:id/clear')
  @ApiOperation({ summary: 'Clear conversation memory' })
  @ApiResponse({ 
    status: 200, 
    description: 'Conversation memory cleared',
  })
  async clearConversation(
    @Req() req: Request,
    @Param('id') conversationId: string,
  ): Promise<{ success: boolean }> {
    const userId = req.user?.id;
    // Implementation would go here
    return { success: true };
  }

  @Get('models')
  @ApiOperation({ summary: 'Get available AI models' })
  @ApiResponse({ 
    status: 200, 
    description: 'List of available AI models',
  })
  async getAvailableModels(): Promise<{
    models: Array<{
      id: string;
      name: string;
      provider: string;
      description: string;
      maxTokens: number;
      pricing: {
        prompt: number;
        completion: number;
      };
    }>;
  }> {
    return {
      models: [
        {
          id: 'gpt-4',
          name: 'GPT-4',
          provider: 'OpenAI',
          description: 'Most capable GPT-4 model',
          maxTokens: 8192,
          pricing: { prompt: 0.03, completion: 0.06 },
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'OpenAI',
          description: 'Fast and efficient model',
          maxTokens: 4096,
          pricing: { prompt: 0.0015, completion: 0.002 },
        },
        {
          id: 'claude-3-sonnet-20240229',
          name: 'Claude 3 Sonnet',
          provider: 'Anthropic',
          description: 'Balanced performance and speed',
          maxTokens: 200000,
          pricing: { prompt: 0.003, completion: 0.015 },
        },
      ],
    };
  }

  @Get('usage')
  @ApiOperation({ summary: 'Get user AI usage statistics' })
  @ApiResponse({ 
    status: 200, 
    description: 'User usage statistics',
  })
  async getUsageStats(
    @Req() req: Request,
    @Query('period') period: string = '30d',
  ): Promise<{
    totalTokens: number;
    totalCost: number;
    conversationCount: number;
    messageCount: number;
    modelUsage: Record<string, number>;
    dailyUsage: Array<{
      date: string;
      tokens: number;
      cost: number;
      messages: number;
    }>;
  }> {
    const userId = req.user?.id;
    // Implementation would go here
    return {
      totalTokens: 0,
      totalCost: 0,
      conversationCount: 0,
      messageCount: 0,
      modelUsage: {},
      dailyUsage: [],
    };
  }
}
