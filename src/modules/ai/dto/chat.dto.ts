import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsBoolean, IsA<PERSON>y, IsEnum, ValidateNested, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum AIModel {
  GPT_4 = 'gpt-4',
  GPT_4_TURBO = 'gpt-4-turbo',
  GPT_3_5_TURBO = 'gpt-3.5-turbo',
  CLAUDE_3_OPUS = 'claude-3-opus-20240229',
  CLAUDE_3_SONNET = 'claude-3-sonnet-20240229',
  CLAUDE_3_HAIKU = 'claude-3-haiku-20240307',
  GEMINI_1_5_PRO = 'gemini-1.5-pro',
  GEMINI_1_5_FLASH = 'gemini-1.5-flash',
  GEMINI_PRO = 'gemini-pro',
}

export class MessageDto {
  @ApiProperty({ description: 'Message role' })
  @IsString()
  @IsEnum(['user', 'assistant', 'system'])
  role: 'user' | 'assistant' | 'system';

  @ApiProperty({ description: 'Message content' })
  @IsString()
  content: string;
}

export class ChatRequestDto {
  @ApiProperty({ description: 'User message' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: 'Conversation ID for continuing chat' })
  @IsOptional()
  @IsString()
  conversationId?: string;

  @ApiPropertyOptional({ description: 'AI model to use', enum: AIModel })
  @IsOptional()
  @IsEnum(AIModel)
  model?: AIModel;

  @ApiPropertyOptional({ description: 'Temperature for AI response', minimum: 0, maximum: 2 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({ description: 'Maximum tokens for response' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4000)
  maxTokens?: number;

  @ApiPropertyOptional({ description: 'Enable streaming response' })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;

  @ApiPropertyOptional({ description: 'System prompt override' })
  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @ApiPropertyOptional({ description: 'Context from knowledge base' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  knowledgeBaseIds?: string[];

  @ApiPropertyOptional({ description: 'Enable function calling' })
  @IsOptional()
  @IsBoolean()
  enableFunctionCalling?: boolean;
}

export class ChatResponseDto {
  @ApiProperty({ description: 'AI response message' })
  message: string;

  @ApiProperty({ description: 'Conversation ID' })
  conversationId: string;

  @ApiProperty({ description: 'Message ID' })
  messageId: string;

  @ApiProperty({ description: 'Model used for response' })
  model: string;

  @ApiProperty({ description: 'Token usage information' })
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };

  @ApiProperty({ description: 'Response cost' })
  cost: number;

  @ApiProperty({ description: 'Response time in milliseconds' })
  responseTime: number;

  @ApiPropertyOptional({ description: 'Function calls made' })
  functionCalls?: Array<{
    name: string;
    arguments: any;
    result: any;
  }>;
}

export class StreamChatDto extends ChatRequestDto {
  @ApiProperty({ description: 'Enable streaming', default: true })
  @IsBoolean()
  stream: boolean = true;
}

export class ConversationDto {
  @ApiProperty({ description: 'Conversation ID' })
  id: string;

  @ApiProperty({ description: 'Conversation title' })
  title: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last updated timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'Message count' })
  messageCount: number;

  @ApiProperty({ description: 'Total tokens used' })
  totalTokens: number;

  @ApiProperty({ description: 'Total cost' })
  totalCost: number;
}

export class ConversationListDto {
  @ApiProperty({ description: 'List of conversations', type: [ConversationDto] })
  conversations: ConversationDto[];

  @ApiProperty({ description: 'Total conversation count' })
  total: number;

  @ApiProperty({ description: 'Current page limit' })
  limit: number;

  @ApiProperty({ description: 'Current page offset' })
  offset: number;
}

export class ConversationDetailDto extends ConversationDto {
  @ApiProperty({ description: 'Conversation messages', type: [MessageDto] })
  messages: MessageDto[];
}

export class ModelDto {
  @ApiProperty({ description: 'Model ID' })
  id: string;

  @ApiProperty({ description: 'Model name' })
  name: string;

  @ApiProperty({ description: 'Model provider' })
  provider: string;

  @ApiProperty({ description: 'Model description' })
  description: string;

  @ApiProperty({ description: 'Maximum tokens' })
  maxTokens: number;

  @ApiProperty({ description: 'Pricing information' })
  pricing: {
    prompt: number;
    completion: number;
  };
}

export class ModelsListDto {
  @ApiProperty({ description: 'Available models', type: [ModelDto] })
  models: ModelDto[];
}

export class DailyUsageDto {
  @ApiProperty({ description: 'Date' })
  date: string;

  @ApiProperty({ description: 'Tokens used' })
  tokens: number;

  @ApiProperty({ description: 'Cost incurred' })
  cost: number;

  @ApiProperty({ description: 'Messages sent' })
  messages: number;
}

export class UsageStatsDto {
  @ApiProperty({ description: 'Total tokens used' })
  totalTokens: number;

  @ApiProperty({ description: 'Total cost' })
  totalCost: number;

  @ApiProperty({ description: 'Total conversations' })
  conversationCount: number;

  @ApiProperty({ description: 'Total messages' })
  messageCount: number;

  @ApiProperty({ description: 'Model usage breakdown' })
  modelUsage: Record<string, number>;

  @ApiProperty({ description: 'Daily usage statistics', type: [DailyUsageDto] })
  dailyUsage: DailyUsageDto[];
}

export class StreamChunkDto {
  @ApiProperty({ description: 'Chunk type' })
  type: 'start' | 'chunk' | 'function_call' | 'error' | 'done';

  @ApiProperty({ description: 'Content data' })
  content?: string;

  @ApiProperty({ description: 'Delta content for streaming' })
  delta?: string;

  @ApiProperty({ description: 'Function call information' })
  functionCall?: {
    name: string;
    arguments: any;
  };

  @ApiProperty({ description: 'Error information' })
  error?: {
    message: string;
    code: string;
  };

  @ApiProperty({ description: 'Metadata' })
  metadata?: {
    model: string;
    tokens: number;
    cost: number;
  };
}

export class ClearConversationDto {
  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'Message' })
  message: string;
}
