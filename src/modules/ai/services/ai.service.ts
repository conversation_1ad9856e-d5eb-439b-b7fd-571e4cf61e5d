import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import { OpenAIProvider } from '../providers/openai.provider';
import { AnthropicProvider } from '../providers/anthropic.provider';
import { ConversationMemoryService } from './conversation-memory.service';
import { FunctionCallingService } from './function-calling.service';
import { 
  ChatRequestDto, 
  ChatResponseDto, 
  AIModel 
} from '../dto/chat.dto';
import { 
  IAIProvider, 
  AIMessage, 
  AIRequestOptions, 
  AIResponse 
} from '../interfaces/ai-provider.interface';

@Injectable()
export class AIService {
  private readonly logger = new Logger(AIService.name);
  private readonly providers: Map<string, IAIProvider> = new Map();
  private readonly defaultModel: string;
  private readonly maxRetries: number;
  private readonly retryDelay: number;

  constructor(
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
    private openaiProvider: OpenAIProvider,
    private anthropicProvider: AnthropicProvider,
    private memoryService: ConversationMemoryService,
    private functionCallingService: FunctionCallingService,
  ) {
    this.defaultModel = this.configService.get<string>('DEFAULT_AI_MODEL', AIModel.GPT_3_5_TURBO);
    this.maxRetries = this.configService.get<number>('AI_MAX_RETRIES', 3);
    this.retryDelay = this.configService.get<number>('AI_RETRY_DELAY', 1000);

    // Register providers
    this.providers.set('openai', this.openaiProvider);
    this.providers.set('anthropic', this.anthropicProvider);
  }

  async chat(userId: string, request: ChatRequestDto): Promise<ChatResponseDto> {
    const startTime = Date.now();

    try {
      // Create a simple conversation ID if not provided
      const conversationId = request.conversationId || uuidv4();

      // Get conversation memory (simplified)
      const memoryMessages = await this.memoryService.getConversationMemory(conversationId);

      // Build messages array
      const messages = this.buildSimpleMessagesArray(request, memoryMessages);

      // Determine AI provider and model
      const { provider, model } = this.getProviderAndModel(request.model);

      // Prepare AI request options
      const aiOptions: AIRequestOptions = {
        model,
        messages,
        temperature: request.temperature ?? 0.7,
        maxTokens: request.maxTokens ?? 1000,
        functions: request.enableFunctionCalling ? await this.functionCallingService.getAvailableFunctions() : undefined,
        functionCall: request.enableFunctionCalling ? 'auto' : 'none',
      };

      // Generate AI response with retry logic
      const aiResponse = await this.generateWithRetry(provider, aiOptions);

      // Handle function calls if present (simplified)
      if (aiResponse.message.functionCall || aiResponse.message.toolCalls) {
        return await this.handleSimpleFunctionCalls(conversationId, aiResponse, aiOptions, provider);
      }

      // Update memory (simplified)
      await this.memoryService.addMessageToMemory(conversationId, {
        role: 'user',
        content: request.message,
      });
      await this.memoryService.addMessageToMemory(conversationId, aiResponse.message);

      // Emit events (simplified)
      this.eventEmitter.emit('ai.response.generated', {
        userId,
        conversationId,
        model: aiResponse.model,
        usage: aiResponse.usage,
        cost: aiResponse.cost,
      });

      return {
        message: aiResponse.message.content,
        conversationId,
        messageId: uuidv4(), // Generate a simple message ID
        model: aiResponse.model,
        usage: aiResponse.usage,
        cost: aiResponse.cost || 0,
        responseTime: Date.now() - startTime,
      };

    } catch (error) {
      this.logger.error(`Chat error for user ${userId}:`, error);
      throw error;
    }
  }

  async* streamChat(userId: string, request: ChatRequestDto): AsyncIterable<any> {
    // Implementation for streaming chat
    // Similar to chat() but yields chunks as they arrive
    // This would be implemented similarly but with streaming response handling
  }

  private buildSimpleMessagesArray(request: ChatRequestDto, memoryMessages: AIMessage[]): AIMessage[] {
    const messages: AIMessage[] = [];

    // Add conversation history from memory
    messages.push(...memoryMessages);

    // Add current user message
    messages.push({
      role: 'user',
      content: request.message,
    });

    return messages;
  }

  private async handleSimpleFunctionCalls(
    conversationId: string,
    aiResponse: AIResponse,
    aiOptions: AIRequestOptions,
    provider: IAIProvider
  ): Promise<ChatResponseDto> {
    // Simplified function call handling without database operations
    let functionResults = null;
    if (aiResponse.message.functionCall) {
      functionResults = await this.functionCallingService.executeFunction(
        aiResponse.message.functionCall.name,
        aiResponse.message.functionCall.arguments
      );
    }

    // Add function results to messages and get final response
    const updatedMessages = [...aiOptions.messages, aiResponse.message];
    if (functionResults) {
      updatedMessages.push({
        role: 'function',
        content: JSON.stringify(functionResults),
      });
    }

    const finalResponse = await provider.generateResponse({
      ...aiOptions,
      messages: updatedMessages,
    });

    return {
      message: finalResponse.message.content,
      conversationId,
      messageId: uuidv4(),
      model: finalResponse.model,
      usage: finalResponse.usage,
      cost: finalResponse.cost || 0,
      responseTime: 0,
    };
  }



  private getProviderAndModel(requestedModel?: AIModel): { provider: IAIProvider; model: string } {
    const model = requestedModel || this.defaultModel;
    
    if (model.startsWith('gpt-')) {
      return { provider: this.providers.get('openai')!, model };
    } else if (model.startsWith('claude-')) {
      return { provider: this.providers.get('anthropic')!, model };
    }
    
    // Default to OpenAI
    return { provider: this.providers.get('openai')!, model: this.defaultModel };
  }

  private async generateWithRetry(provider: IAIProvider, options: AIRequestOptions): Promise<AIResponse> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await provider.generateResponse(options);
      } catch (error) {
        lastError = error;
        this.logger.warn(`AI generation attempt ${attempt} failed:`, error.message);
        
        if (attempt < this.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        }
      }
    }
    
    throw lastError!;
  }


}
