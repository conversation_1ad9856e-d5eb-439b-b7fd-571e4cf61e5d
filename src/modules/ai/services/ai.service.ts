import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';

import { User, Conversation, Message, MessageRole, MessageStatus, ConversationType } from '../../../entities';
import { OpenAIProvider } from '../providers/openai.provider';
import { AnthropicProvider } from '../providers/anthropic.provider';
import { ConversationMemoryService } from './conversation-memory.service';
import { FunctionCallingService } from './function-calling.service';
import { 
  ChatRequestDto, 
  ChatResponseDto, 
  AIModel 
} from '../dto/chat.dto';
import { 
  IAIProvider, 
  AIMessage, 
  AIRequestOptions, 
  AIResponse 
} from '../interfaces/ai-provider.interface';

@Injectable()
export class AIService {
  private readonly logger = new Logger(AIService.name);
  private readonly providers: Map<string, IAIProvider> = new Map();
  private readonly defaultModel: string;
  private readonly maxRetries: number;
  private readonly retryDelay: number;

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Conversation)
    private conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    private configService: ConfigService,
    private eventEmitter: EventEmitter2,
    private openaiProvider: OpenAIProvider,
    private anthropicProvider: AnthropicProvider,
    private memoryService: ConversationMemoryService,
    private functionCallingService: FunctionCallingService,
  ) {
    this.defaultModel = this.configService.get<string>('DEFAULT_AI_MODEL', AIModel.GPT_3_5_TURBO);
    this.maxRetries = this.configService.get<number>('AI_MAX_RETRIES', 3);
    this.retryDelay = this.configService.get<number>('AI_RETRY_DELAY', 1000);

    // Register providers
    this.providers.set('openai', this.openaiProvider);
    this.providers.set('anthropic', this.anthropicProvider);
  }

  async chat(userId: string, request: ChatRequestDto): Promise<ChatResponseDto> {
    const startTime = Date.now();
    
    try {
      // Get or create conversation
      const conversation = await this.getOrCreateConversation(userId, request.conversationId);
      
      // Get conversation context and memory
      const context = await this.memoryService.getConversationContext(conversation.id);
      
      // Build messages array
      const messages = await this.buildMessagesArray(request, context.messages, context.metadata);
      
      // Determine AI provider and model
      const { provider, model } = this.getProviderAndModel(request.model);
      
      // Prepare AI request options
      const aiOptions: AIRequestOptions = {
        model,
        messages,
        temperature: request.temperature ?? context.metadata.userPreferences?.temperature ?? 0.7,
        maxTokens: request.maxTokens ?? context.metadata.userPreferences?.maxTokens ?? 1000,
        functions: request.enableFunctionCalling ? await this.functionCallingService.getAvailableFunctions() : undefined,
        functionCall: request.enableFunctionCalling ? 'auto' : 'none',
      };

      // Generate AI response with retry logic
      const aiResponse = await this.generateWithRetry(provider, aiOptions);
      
      // Handle function calls if present
      if (aiResponse.message.functionCall || aiResponse.message.toolCalls) {
        return await this.handleFunctionCalls(conversation, aiResponse, aiOptions, provider);
      }

      // Save user message
      const userMessage = await this.saveMessage(conversation.id, {
        role: MessageRole.USER,
        content: request.message,
        status: MessageStatus.COMPLETED,
      });

      // Save AI response
      const assistantMessage = await this.saveMessage(conversation.id, {
        role: MessageRole.ASSISTANT,
        content: aiResponse.message.content,
        status: MessageStatus.COMPLETED,
        metadata: {
          model: aiResponse.model,
          temperature: aiOptions.temperature,
          maxTokens: aiOptions.maxTokens,
          finishReason: aiResponse.finishReason,
        },
        promptTokens: aiResponse.usage.promptTokens,
        completionTokens: aiResponse.usage.completionTokens,
        totalTokens: aiResponse.usage.totalTokens,
        cost: aiResponse.cost,
        responseTimeMs: Date.now() - startTime,
      });

      // Update conversation
      await this.updateConversation(conversation.id, {
        messageCount: conversation.messageCount + 2,
        totalTokensUsed: conversation.totalTokensUsed + aiResponse.usage.totalTokens,
        totalCost: conversation.totalCost + (aiResponse.cost || 0),
        lastMessageAt: new Date(),
      });

      // Update memory
      await this.memoryService.addMessageToMemory(conversation.id, {
        role: 'user',
        content: request.message,
      });
      await this.memoryService.addMessageToMemory(conversation.id, aiResponse.message);

      // Emit events
      this.eventEmitter.emit('ai.response.generated', {
        userId,
        conversationId: conversation.id,
        messageId: assistantMessage.id,
        model: aiResponse.model,
        usage: aiResponse.usage,
        cost: aiResponse.cost,
      });

      return {
        message: aiResponse.message.content,
        conversationId: conversation.id,
        messageId: assistantMessage.id,
        model: aiResponse.model,
        usage: aiResponse.usage,
        cost: aiResponse.cost || 0,
        responseTime: Date.now() - startTime,
      };

    } catch (error) {
      this.logger.error(`Chat error for user ${userId}:`, error);
      throw error;
    }
  }

  async* streamChat(userId: string, request: ChatRequestDto): AsyncIterable<any> {
    // Implementation for streaming chat
    // Similar to chat() but yields chunks as they arrive
    // This would be implemented similarly but with streaming response handling
  }

  private async getOrCreateConversation(userId: string, conversationId?: string): Promise<Conversation> {
    if (conversationId) {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId, userId },
        relations: ['user'],
      });
      
      if (!conversation) {
        throw new BadRequestException('Conversation not found');
      }
      
      return conversation;
    }

    // Create new conversation
    const conversation = this.conversationRepository.create({
      id: uuidv4(),
      title: 'New Conversation',
      type: ConversationType.CHAT,
      userId,
      messageCount: 0,
      totalTokensUsed: 0,
      totalCost: 0,
    });

    return await this.conversationRepository.save(conversation);
  }

  private async buildMessagesArray(
    request: ChatRequestDto, 
    memory: AIMessage[], 
    metadata: any
  ): Promise<AIMessage[]> {
    const messages: AIMessage[] = [];

    // Add system prompt
    const systemPrompt = request.systemPrompt || 
                        metadata.conversationMetadata?.systemPrompt || 
                        this.configService.get<string>('DEFAULT_SYSTEM_PROMPT', 
                          'You are a helpful AI assistant.');
    
    messages.push({
      role: 'system',
      content: systemPrompt,
    });

    // Add conversation memory
    messages.push(...memory);

    // Add current user message
    messages.push({
      role: 'user',
      content: request.message,
    });

    return messages;
  }

  private getProviderAndModel(requestedModel?: AIModel): { provider: IAIProvider; model: string } {
    const model = requestedModel || this.defaultModel;
    
    if (model.startsWith('gpt-')) {
      return { provider: this.providers.get('openai')!, model };
    } else if (model.startsWith('claude-')) {
      return { provider: this.providers.get('anthropic')!, model };
    }
    
    // Default to OpenAI
    return { provider: this.providers.get('openai')!, model: this.defaultModel };
  }

  private async generateWithRetry(provider: IAIProvider, options: AIRequestOptions): Promise<AIResponse> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await provider.generateResponse(options);
      } catch (error) {
        lastError = error;
        this.logger.warn(`AI generation attempt ${attempt} failed:`, error.message);
        
        if (attempt < this.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        }
      }
    }
    
    throw lastError!;
  }

  private async handleFunctionCalls(
    conversation: Conversation,
    aiResponse: AIResponse,
    aiOptions: AIRequestOptions,
    provider: IAIProvider
  ): Promise<ChatResponseDto> {
    // Implementation for handling function calls
    // This would execute the functions and continue the conversation
    throw new Error('Function calling not yet implemented');
  }

  private async saveMessage(conversationId: string, messageData: Partial<Message>): Promise<Message> {
    const message = this.messageRepository.create({
      id: uuidv4(),
      conversationId,
      ...messageData,
    });

    return await this.messageRepository.save(message);
  }

  private async updateConversation(conversationId: string, updates: Partial<Conversation>): Promise<void> {
    await this.conversationRepository.update(conversationId, {
      ...updates,
      updatedAt: new Date(),
    });
  }
}
