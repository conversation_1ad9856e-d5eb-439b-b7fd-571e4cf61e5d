import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { Conversation, Message, MessageRole } from '../../../entities';
import { AIMessage } from '../interfaces/ai-provider.interface';

@Injectable()
export class ConversationMemoryService {
  private readonly logger = new Logger(ConversationMemoryService.name);
  private readonly redis: Redis;
  private readonly maxMemoryMessages: number;
  private readonly memoryTtl: number; // seconds

  constructor(
    @InjectRepository(Conversation)
    private conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    private configService: ConfigService,
  ) {
    this.redis = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 0),
    });

    this.maxMemoryMessages = this.configService.get<number>('MAX_MEMORY_MESSAGES', 20);
    this.memoryTtl = this.configService.get<number>('MEMORY_TTL', 3600); // 1 hour
  }

  async getConversationMemory(conversationId: string): Promise<AIMessage[]> {
    try {
      // Try to get from Redis cache first
      const cacheKey = `conversation:${conversationId}:memory`;
      const cachedMemory = await this.redis.get(cacheKey);
      
      if (cachedMemory) {
        this.logger.debug(`Retrieved conversation memory from cache: ${conversationId}`);
        return JSON.parse(cachedMemory);
      }

      // If not in cache, get from database
      const messages = await this.messageRepository.find({
        where: { conversationId },
        order: { createdAt: 'ASC' },
        take: this.maxMemoryMessages,
      });

      const aiMessages: AIMessage[] = messages.map(msg => ({
        role: msg.role as any,
        content: msg.content,
        functionCall: msg.metadata?.functionCall,
        toolCalls: msg.metadata?.toolCalls,
      }));

      // Cache the memory
      await this.redis.setex(cacheKey, this.memoryTtl, JSON.stringify(aiMessages));
      
      this.logger.debug(`Retrieved conversation memory from database: ${conversationId}`);
      return aiMessages;
    } catch (error) {
      this.logger.error(`Error retrieving conversation memory: ${error.message}`);
      return [];
    }
  }

  async addMessageToMemory(conversationId: string, message: AIMessage): Promise<void> {
    try {
      const cacheKey = `conversation:${conversationId}:memory`;
      const currentMemory = await this.getConversationMemory(conversationId);
      
      // Add new message
      currentMemory.push(message);
      
      // Keep only the last N messages
      if (currentMemory.length > this.maxMemoryMessages) {
        currentMemory.splice(0, currentMemory.length - this.maxMemoryMessages);
      }

      // Update cache
      await this.redis.setex(cacheKey, this.memoryTtl, JSON.stringify(currentMemory));
      
      this.logger.debug(`Added message to conversation memory: ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error adding message to memory: ${error.message}`);
    }
  }

  async clearConversationMemory(conversationId: string): Promise<void> {
    try {
      const cacheKey = `conversation:${conversationId}:memory`;
      await this.redis.del(cacheKey);
      
      this.logger.debug(`Cleared conversation memory: ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error clearing conversation memory: ${error.message}`);
    }
  }

  async getConversationContext(conversationId: string): Promise<{
    messages: AIMessage[];
    metadata: any;
  }> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
        relations: ['user'],
      });

      if (!conversation) {
        throw new Error(`Conversation not found: ${conversationId}`);
      }

      const messages = await this.getConversationMemory(conversationId);
      
      return {
        messages,
        metadata: {
          conversationType: conversation.type,
          userPreferences: conversation.user?.preferences,
          conversationMetadata: conversation.metadata,
          context: conversation.context,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting conversation context: ${error.message}`);
      throw error;
    }
  }

  async updateConversationSummary(conversationId: string, summary: string): Promise<void> {
    try {
      await this.conversationRepository.update(conversationId, {
        summary,
        updatedAt: new Date(),
      });
      
      this.logger.debug(`Updated conversation summary: ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error updating conversation summary: ${error.message}`);
    }
  }
}
