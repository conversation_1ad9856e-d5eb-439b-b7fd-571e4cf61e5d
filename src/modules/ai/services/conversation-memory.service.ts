import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIMessage } from '../interfaces/ai-provider.interface';

@Injectable()
export class ConversationMemoryService {
  private readonly logger = new Logger(ConversationMemoryService.name);
  private readonly maxMemoryMessages: number;
  private readonly memoryTtl: number; // seconds
  private readonly memoryStore: Map<string, { data: AIMessage[], timestamp: number }> = new Map();

  constructor(
    private configService: ConfigService,
  ) {
    this.maxMemoryMessages = this.configService.get<number>('MAX_MEMORY_MESSAGES', 20);
    this.memoryTtl = this.configService.get<number>('MEMORY_TTL', 3600); // 1 hour
  }

  async getConversationMemory(conversationId: string): Promise<AIMessage[]> {
    try {
      // Get from in-memory store
      const cacheKey = `conversation:${conversationId}:memory`;
      const cachedEntry = this.memoryStore.get(cacheKey);

      if (cachedEntry) {
        // Check if entry is still valid (not expired)
        const now = Date.now();
        if (now - cachedEntry.timestamp < this.memoryTtl * 1000) {
          this.logger.debug(`Retrieved conversation memory from cache: ${conversationId}`);
          return cachedEntry.data;
        } else {
          // Remove expired entry
          this.memoryStore.delete(cacheKey);
        }
      }

      // Return empty array if no memory found
      this.logger.debug(`No conversation memory found for: ${conversationId}`);
      return [];
    } catch (error) {
      this.logger.error(`Error retrieving conversation memory: ${error.message}`);
      return [];
    }
  }

  async addMessageToMemory(conversationId: string, message: AIMessage): Promise<void> {
    try {
      const cacheKey = `conversation:${conversationId}:memory`;
      const currentMemory = await this.getConversationMemory(conversationId);

      // Add new message
      currentMemory.push(message);

      // Keep only the last N messages
      if (currentMemory.length > this.maxMemoryMessages) {
        currentMemory.splice(0, currentMemory.length - this.maxMemoryMessages);
      }

      // Update in-memory store
      this.memoryStore.set(cacheKey, {
        data: currentMemory,
        timestamp: Date.now()
      });

      this.logger.debug(`Added message to conversation memory: ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error adding message to memory: ${error.message}`);
    }
  }

  async clearConversationMemory(conversationId: string): Promise<void> {
    try {
      const cacheKey = `conversation:${conversationId}:memory`;
      this.memoryStore.delete(cacheKey);

      this.logger.debug(`Cleared conversation memory: ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error clearing conversation memory: ${error.message}`);
    }
  }


}
