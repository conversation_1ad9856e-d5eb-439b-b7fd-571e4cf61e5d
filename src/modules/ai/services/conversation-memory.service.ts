import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { AIMessage } from '../interfaces/ai-provider.interface';

@Injectable()
export class ConversationMemoryService {
  private readonly logger = new Logger(ConversationMemoryService.name);
  private readonly redis: Redis;
  private readonly maxMemoryMessages: number;
  private readonly memoryTtl: number; // seconds

  constructor(
    private configService: ConfigService,
  ) {
    this.redis = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 0),
    });

    this.maxMemoryMessages = this.configService.get<number>('MAX_MEMORY_MESSAGES', 20);
    this.memoryTtl = this.configService.get<number>('MEMORY_TTL', 3600); // 1 hour
  }

  async getConversationMemory(conversationId: string): Promise<AIMessage[]> {
    try {
      // Get from Redis cache only (no database fallback)
      const cacheKey = `conversation:${conversationId}:memory`;
      const cachedMemory = await this.redis.get(cacheKey);

      if (cachedMemory) {
        this.logger.debug(`Retrieved conversation memory from cache: ${conversationId}`);
        return JSON.parse(cachedMemory);
      }

      // Return empty array if no memory found
      this.logger.debug(`No conversation memory found for: ${conversationId}`);
      return [];
    } catch (error) {
      this.logger.error(`Error retrieving conversation memory: ${error.message}`);
      return [];
    }
  }

  async addMessageToMemory(conversationId: string, message: AIMessage): Promise<void> {
    try {
      const cacheKey = `conversation:${conversationId}:memory`;
      const currentMemory = await this.getConversationMemory(conversationId);
      
      // Add new message
      currentMemory.push(message);
      
      // Keep only the last N messages
      if (currentMemory.length > this.maxMemoryMessages) {
        currentMemory.splice(0, currentMemory.length - this.maxMemoryMessages);
      }

      // Update cache
      await this.redis.setex(cacheKey, this.memoryTtl, JSON.stringify(currentMemory));
      
      this.logger.debug(`Added message to conversation memory: ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error adding message to memory: ${error.message}`);
    }
  }

  async clearConversationMemory(conversationId: string): Promise<void> {
    try {
      const cacheKey = `conversation:${conversationId}:memory`;
      await this.redis.del(cacheKey);
      
      this.logger.debug(`Cleared conversation memory: ${conversationId}`);
    } catch (error) {
      this.logger.error(`Error clearing conversation memory: ${error.message}`);
    }
  }


}
