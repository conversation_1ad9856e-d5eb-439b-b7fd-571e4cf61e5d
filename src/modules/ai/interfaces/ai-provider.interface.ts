export interface AIMessage {
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
  functionCall?: {
    name: string;
    arguments: string;
  };
  toolCalls?: Array<{
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
  }>;
}

export interface AIFunction {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface AIRequestOptions {
  model: string;
  messages: AIMessage[];
  temperature?: number;
  maxTokens?: number;
  functions?: AIFunction[];
  functionCall?: 'auto' | 'none' | { name: string };
  stream?: boolean;
}

export interface AIResponse {
  message: AIMessage;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  finishReason: string;
  cost?: number;
}

export interface AIStreamChunk {
  delta: {
    role?: string;
    content?: string;
    functionCall?: {
      name?: string;
      arguments?: string;
    };
  };
  finishReason?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface IAIProvider {
  generateResponse(options: AIRequestOptions): Promise<AIResponse>;
  generateStreamResponse(options: AIRequestOptions): AsyncIterable<AIStreamChunk>;
  calculateCost(model: string, promptTokens: number, completionTokens: number): number;
  getAvailableModels(): string[];
}
