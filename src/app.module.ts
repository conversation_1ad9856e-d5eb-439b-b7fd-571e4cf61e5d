import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { LoggerModule } from './core/logger/logger.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { HttpExceptionFilter } from './core/exceptions/http-exception.filter';
import { HttpModule } from '@nestjs/axios';
import { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';
import { PushController } from './modules/push/push.controller';
import { PushService } from './modules/push/push.service';
import { PushModule } from './modules/push/push.module';
import { JwtService } from '@nestjs/jwt';
import { DeviceModule } from './modules/device/device.module';
import { DeviceService } from './modules/device/device.service';
import { PushMessageService } from './modules/push/push.message.service';
import { AuthService } from './modules/auth/auth.service';
import { MWHttpService } from './core/service/http.service';

@Module({
  imports: [ConfigModule.forRoot(), LoggerModule, PushModule, DeviceModule,
  HttpModule.registerAsync({
    useFactory: () => ({
      timeout: 30000,
      maxRedirects: 5,
    }),
  }),
  ],
  controllers: [AppController, PushController],
  providers: [AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    PushService,
    JwtService,
    DeviceService,
    PushMessageService,
    AuthService,
    MWHttpService
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) { }
}
