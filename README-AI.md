# AI Backend Service

A comprehensive NestJS-based AI backend service that provides chat capabilities, knowledge base management, and API integration features.

## Features

### 🤖 AI Services
- **Multi-Provider Support**: OpenAI GPT models and Anthropic Claude
- **Streaming Responses**: Real-time chat with Server-Sent Events
- **Conversation Memory**: Redis-based conversation history management
- **Function Calling**: AI-driven API interactions and tool use
- **Cost Tracking**: Token usage and cost calculation per conversation

### 📚 Knowledge Base
- **Document Management**: Upload and process various document types
- **Vector Search**: Semantic search using embeddings (Pinecone integration)
- **RAG Implementation**: Retrieval Augmented Generation for enhanced AI responses
- **Document Processing**: Intelligent chunking and metadata extraction
- **Multiple Formats**: Support for text, API docs, manuals, FAQs, and more

### 🔌 API Integration
- **Dynamic API Calling**: Execute API calls with schema validation
- **Schema Parsing**: Import from OpenAPI/Swagger/Postman collections
- **Response Caching**: Redis-based API response caching
- **Authentication Support**: Bearer, Basic, API Key, and OAuth2
- **Rate Limiting**: Configurable rate limits per endpoint

## Quick Start

### Prerequisites
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Docker & Docker Compose (optional)

### Installation

1. **Clone and install dependencies**:
```bash
npm install --legacy-peer-deps
```

2. **Environment setup**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Database setup**:
```bash
# Run migrations
npm run migration:run
```

4. **Start the AI backend**:
```bash
# Development
npm run start:ai:dev

# Production
npm run build:ai
npm run start:ai:prod
```

### Docker Deployment

```bash
# Start all services
docker-compose up -d

# Build and run AI backend only
npm run docker:ai:build
npm run docker:ai:run
```

## API Documentation

Once running, access the interactive API documentation at:
- **Swagger UI**: http://localhost:3000/api/docs
- **Health Check**: http://localhost:3000/health

## Configuration

### Required Environment Variables

```env
# AI Provider APIs
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Vector Database
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_INDEX_NAME=ai-knowledge-base

# Database
DB_HOST=localhost
DB_USERNAME=ai_backend
DB_PASSWORD=your_password
DB_DATABASE=ai_backend_db

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
```

## API Endpoints

### AI Chat
```http
POST /api/v1/ai/chat
POST /api/v1/ai/stream-chat
GET  /api/v1/ai/conversations
GET  /api/v1/ai/models
```

### Knowledge Base
```http
POST /api/v1/knowledge-base/upload
POST /api/v1/knowledge-base/search
GET  /api/v1/knowledge-base/documents
DELETE /api/v1/knowledge-base/documents/:id
```

### API Integration
```http
POST /api/v1/api-integration/call
POST /api/v1/api-integration/parse-schema
POST /api/v1/api-integration/import-endpoints
POST /api/v1/api-integration/test/:endpointId
```

## Usage Examples

### Chat with AI
```typescript
const response = await fetch('/api/v1/ai/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    message: 'Hello, how can you help me?',
    conversationId: 'optional-conversation-id',
    model: 'gpt-4',
    useKnowledgeBase: true
  })
});
```

### Upload Document to Knowledge Base
```typescript
const response = await fetch('/api/v1/knowledge-base/upload', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    title: 'API Documentation',
    content: 'Your document content here...',
    type: 'API_DOCUMENTATION',
    summary: 'Documentation for our REST API'
  })
});
```

### Search Knowledge Base
```typescript
const response = await fetch('/api/v1/knowledge-base/search', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    query: 'How to authenticate API requests?',
    limit: 5,
    threshold: 0.7
  })
});
```

### Call External API
```typescript
const response = await fetch('/api/v1/api-integration/call', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    endpointId: 'endpoint-uuid',
    parameters: {
      queryParams: { limit: 10 },
      headers: { 'X-API-Key': 'your-api-key' }
    }
  })
});
```

## Architecture

### Core Modules
- **AI Module**: Chat, streaming, conversation management
- **Knowledge Base Module**: Document processing, vector search, RAG
- **API Integration Module**: Dynamic API calling, schema parsing

### Database Schema
- **Users**: User management and preferences
- **Conversations**: Chat sessions and metadata
- **Messages**: Individual chat messages with roles
- **KnowledgeBase**: Documents with embeddings and chunks
- **ApiEndpoint**: API endpoint definitions and usage stats

### External Services
- **OpenAI**: GPT models and embeddings
- **Anthropic**: Claude models
- **Pinecone**: Vector database for semantic search
- **In-Memory Storage**: Caching and conversation memory (no Redis required)

## Development

### Project Structure
```
src/
├── entities/           # TypeORM entities
├── modules/
│   ├── ai/            # AI chat and conversation logic
│   ├── knowledge-base/ # Document management and search
│   └── api-integration/ # API calling and schema parsing
├── config/            # Configuration classes
├── core/              # Guards, interceptors, utilities
└── main-ai.ts         # AI backend entry point
```

### Testing
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Coverage
npm run test:cov
```

### Database Migrations
```bash
# Generate migration
npm run migration:generate -- -n MigrationName

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## Monitoring

The service includes built-in monitoring with:
- **Prometheus**: Metrics collection
- **Grafana**: Dashboard visualization
- **Health Checks**: Application health monitoring
- **Logging**: Structured logging with different levels

Access monitoring at:
- **Grafana**: http://localhost:3001 (admin/admin)
- **Prometheus**: http://localhost:9090

## Security

- **JWT Authentication**: Secure API access
- **Rate Limiting**: Prevent API abuse
- **Input Validation**: Request validation with class-validator
- **CORS**: Configurable cross-origin requests
- **Helmet**: Security headers

## Performance

- **Caching**: Redis-based response caching
- **Connection Pooling**: Database connection optimization
- **Compression**: Response compression
- **Streaming**: Efficient real-time responses

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
