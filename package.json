{"name": "drivenmobile-push-notification-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "webpack --config webpack.config.js", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "webpack --config webpack.config.js --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@grpc/grpc-js": "^1.11.1", "@grpc/proto-loader": "^0.7.13", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.3.10", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.3.10", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.3.10", "@nestjs/swagger": "^7.4.0", "@okta/jwt-verifier": "^3.2.2", "class-validator": "^0.14.1", "config": "^3.3.12", "ioredis": "^5.4.1", "firebase-admin": "^13.0.2", "fork-ts-checker-webpack-plugin": "^9.0.2", "helmet": "^7.1.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "run-script-webpack-plugin": "^0.2.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}