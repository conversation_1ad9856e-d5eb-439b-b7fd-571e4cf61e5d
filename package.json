{"name": "drivenmobile-push-notification-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "webpack --config webpack.config.js", "build:ai": "nest build --webpack --webpackPath webpack.ai.config.js", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start:dev": "webpack --config webpack.config.js --watch", "start:ai:dev": "npm run build:ai && node dist/main-ai.js", "start:ai:debug": "nest start main-ai --debug --watch", "start:ai:prod": "node dist/main-ai.js", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:ai:build": "docker build -f Dockerfile.ai -t ai-backend .", "docker:ai:run": "docker-compose up ai-backend", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "migration:generate": "typeorm-ts-node-commonjs migration:generate", "migration:run": "typeorm-ts-node-commonjs migration:run", "migration:revert": "typeorm-ts-node-commonjs migration:revert"}, "dependencies": {"@anthropic-ai/sdk": "^0.55.1", "@grpc/grpc-js": "^1.11.1", "@grpc/proto-loader": "^0.7.13", "@nestjs/axios": "^3.0.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.3.10", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.3.10", "@nestjs/event-emitter": "^2.0.4", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.3.10", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.10", "@nestjs/platform-socket.io": "^10.3.10", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^7.4.0", "@nestjs/throttler": "^5.2.0", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.3.10", "@okta/jwt-verifier": "^3.2.2", "@pinecone-database/pinecone": "^6.1.1", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.12", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "bcrypt": "^5.1.1", "cache-manager": "^5.7.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.4", "config": "^3.3.12", "dotenv": "^16.4.5", "firebase-admin": "^13.0.2", "fork-ts-checker-webpack-plugin": "^9.0.2", "helmet": "^7.1.0", "ioredis": "^5.4.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "openai": "^4.67.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.25", "uuid": "^10.0.0"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.3.10", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/jest": "^29.5.13", "@types/node": "^20.16.11", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.3.3", "run-script-webpack-plugin": "^0.2.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}