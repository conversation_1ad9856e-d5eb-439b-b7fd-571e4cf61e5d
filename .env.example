# Application Configuration
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
CORS_ORIGIN=*

# Database Configuration
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=ai_backend
DB_PASSWORD=your_password
DB_DATABASE=ai_backend_db
DB_SYNCHRONIZE=true
DB_LOGGING=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORGANIZATION=your-openai-organization-id
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7

# Anthropic Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_DEFAULT_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4096

# Pinecone Configuration
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-east-1-aws
PINECONE_INDEX_NAME=ai-knowledge-base
PINECONE_DIMENSIONS=1536

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Caching
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# Document Processing
DEFAULT_CHUNK_SIZE=1000
DEFAULT_CHUNK_OVERLAP=200

# API Integration
API_DEFAULT_TIMEOUT=30000
API_ENABLE_CACHING=true
API_DEFAULT_CACHE_TTL=300
API_BEARER_TOKEN=
API_BASIC_USERNAME=
API_BASIC_PASSWORD=
API_KEY=

# Conversation Memory
CONVERSATION_MEMORY_TTL=3600
MAX_CONVERSATION_HISTORY=50

# Function Calling
ENABLE_FUNCTION_CALLING=true
MAX_FUNCTION_CALLS=5

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
